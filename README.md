# Digital Attendance System

A comprehensive web-based attendance management system with separate interfaces for HR and employees.

## Features

### 🔐 Dual Login System
- **Employee Login**: For staff to mark attendance
- **HR Login**: For administrators to manage attendance and employees

### 👨‍💼 Employee Features
- ✅ Check In/Check Out functionality
- ☕ Break time tracking
- 📊 Personal attendance dashboard
- 📈 Weekly attendance overview
- 👤 Profile management
- 📱 Mobile-responsive design

### 👩‍💼 HR Features
- 📊 Comprehensive dashboard with analytics
- 👥 Employee management
- 📈 Attendance reports and charts
- 📋 Real-time attendance monitoring
- 📊 Statistics and insights
- 🔧 System administration

## Demo Credentials

### Employee Login
- **Employee ID**: `EMP001`
- **Password**: `employee123`

### HR Login
- **Username**: `hr_admin`
- **Password**: `hr123`
- **Department**: `Human Resources`

## File Structure

```
attendance-system/
├── attendance.html          # Main login page
├── employee-dashboard.html  # Employee dashboard
├── hr-dashboard.html       # HR dashboard
├── attendance-styles.css   # Complete styling
├── attendance-script.js    # JavaScript functionality
└── README.md              # Documentation
```

## Getting Started

1. **Open the System**
   - Open `attendance.html` in your web browser
   - Or use a local server for better performance

2. **Employee Access**
   - Select "Employee Login"
   - Enter credentials: EMP001 / employee123
   - Access personal dashboard features

3. **HR Access**
   - Select "HR Login"
   - Enter credentials: hr_admin / hr123 / Human Resources
   - Access administrative features

## Key Functionalities

### Employee Dashboard
- **Attendance Marking**: Real-time check-in/check-out with confirmation
- **Break Management**: Start/end breaks with automatic time tracking
- **Working Hours**: Live calculation of daily working hours
- **Weekly View**: Visual calendar showing attendance patterns
- **Profile**: Personal information and work schedule

### HR Dashboard
- **Overview**: Real-time statistics and charts
- **Employee Management**: Add, edit, and manage employee records
- **Attendance Monitoring**: Live tracking of all employee attendance
- **Reports**: Generate and export attendance reports
- **Analytics**: Visual charts and trends analysis

## Technical Features

### 🎨 Modern UI/UX
- Clean, professional design
- Responsive layout for all devices
- Smooth animations and transitions
- Intuitive navigation

### 💾 Data Management
- Local storage for demo purposes
- Session management
- Real-time updates
- Data persistence

### 🔒 Security Features
- Role-based access control
- Session validation
- Secure login system
- Protected routes

### 📱 Responsive Design
- Mobile-first approach
- Tablet and desktop optimization
- Touch-friendly interface
- Adaptive layouts

## Browser Compatibility

- ✅ Chrome (recommended)
- ✅ Firefox
- ✅ Safari
- ✅ Edge
- ✅ Mobile browsers

## Usage Instructions

### For Employees
1. Log in with your Employee ID and password
2. Click "Check In" to start your workday
3. Use "Start Break" when taking breaks
4. Click "Check Out" when ending your workday
5. View your attendance history in the "My Attendance" section

### For HR Administrators
1. Log in with HR credentials
2. Monitor real-time attendance on the overview dashboard
3. Manage employees in the "Employees" section
4. Generate reports from the "Reports" section
5. View detailed analytics and trends

## Customization

The system is built with modular CSS and JavaScript, making it easy to:
- Customize colors and branding
- Add new features
- Integrate with backend systems
- Modify user roles and permissions

## Future Enhancements

- 🔗 Backend API integration
- 📧 Email notifications
- 📊 Advanced reporting
- 🔔 Push notifications
- 📱 Mobile app
- 🌐 Multi-language support
- 🔐 Advanced authentication (2FA)
- 📈 Advanced analytics

## Support

For technical support or feature requests, please refer to the documentation or contact the development team.

---

**Note**: This is a demo system using local storage. For production use, integrate with a proper backend database and authentication system.
