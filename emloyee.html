<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Dashboard - Digital Attendance</title>
    <link rel="stylesheet" href="attendance-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="dashboard-body">
    <!-- Sidebar -->
    <div class="sidebar employee-sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-clock"></i>
                <h3>Employee Portal</h3>
            </div>
        </div>
        <nav class="sidebar-nav">
            <a href="#dashboard" class="nav-item active">
                <i class="fas fa-tachometer-alt"></i>
                <span>Dashboard</span>
            </a>
            <a href="#attendance" class="nav-item">
                <i class="fas fa-calendar-check"></i>
                <span>My Attendance</span>
            </a>
            <a href="#profile" class="nav-item">
                <i class="fas fa-user"></i>
                <span>Profile</span>
            </a>
            <a href="#requests" class="nav-item">
                <i class="fas fa-paper-plane"></i>
                <span>Leave Requests</span>
            </a>
            <a href="#schedule" class="nav-item">
                <i class="fas fa-calendar-alt"></i>
                <span>Schedule</span>
            </a>
        </nav>
        <div class="sidebar-footer">
            <a href="attendance.html" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>Welcome, John Smith</h1>
            </div>
            <div class="header-right">
                <div class="current-time">
                    <i class="fas fa-clock"></i>
                    <span id="currentTime"></span>
                </div>
                <div class="user-profile">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="John Smith">
                    <span>John Smith</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <!-- Attendance Actions -->
                <div class="attendance-actions">
                    <div class="action-card check-in">
                        <div class="action-icon">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <div class="action-info">
                            <h3>Check In</h3>
                            <p>Start your workday</p>
                            <button class="action-btn" id="checkInBtn" onclick="checkIn()">
                                <i class="fas fa-play"></i>
                                Check In
                            </button>
                        </div>
                        <div class="action-status">
                            <span id="checkInTime">--:--</span>
                        </div>
                    </div>
                    
                    <div class="action-card check-out">
                        <div class="action-icon">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <div class="action-info">
                            <h3>Check Out</h3>
                            <p>End your workday</p>
                            <button class="action-btn" id="checkOutBtn" onclick="checkOut()" disabled>
                                <i class="fas fa-stop"></i>
                                Check Out
                            </button>
                        </div>
                        <div class="action-status">
                            <span id="checkOutTime">--:--</span>
                        </div>
                    </div>
                    
                    <div class="action-card break">
                        <div class="action-icon">
                            <i class="fas fa-coffee"></i>
                        </div>
                        <div class="action-info">
                            <h3>Break</h3>
                            <p>Take a break</p>
                            <button class="action-btn" id="breakBtn" onclick="toggleBreak()" disabled>
                                <i class="fas fa-pause"></i>
                                Start Break
                            </button>
                        </div>
                        <div class="action-status">
                            <span id="breakTime">0m</span>
                        </div>
                    </div>
                </div>

                <!-- Today's Summary -->
                <div class="today-summary">
                    <h3>Today's Summary</h3>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <div class="summary-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="summary-info">
                                <h4>Working Hours</h4>
                                <span id="workingHours">0h 0m</span>
                            </div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon">
                                <i class="fas fa-coffee"></i>
                            </div>
                            <div class="summary-info">
                                <h4>Break Time</h4>
                                <span id="totalBreakTime">0h 0m</span>
                            </div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="summary-info">
                                <h4>Status</h4>
                                <span id="todayStatus" class="status-badge">Not Started</span>
                            </div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="summary-info">
                                <h4>Productivity</h4>
                                <span id="productivity">--</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Weekly Overview -->
                <div class="weekly-overview">
                    <h3>This Week's Attendance</h3>
                    <div class="week-calendar">
                        <div class="day-card">
                            <div class="day-header">
                                <span class="day-name">Mon</span>
                                <span class="day-date">15</span>
                            </div>
                            <div class="day-status present">
                                <i class="fas fa-check"></i>
                                <span>8h 30m</span>
                            </div>
                        </div>
                        <div class="day-card">
                            <div class="day-header">
                                <span class="day-name">Tue</span>
                                <span class="day-date">16</span>
                            </div>
                            <div class="day-status present">
                                <i class="fas fa-check"></i>
                                <span>8h 15m</span>
                            </div>
                        </div>
                        <div class="day-card">
                            <div class="day-header">
                                <span class="day-name">Wed</span>
                                <span class="day-date">17</span>
                            </div>
                            <div class="day-status present">
                                <i class="fas fa-check"></i>
                                <span>8h 45m</span>
                            </div>
                        </div>
                        <div class="day-card">
                            <div class="day-header">
                                <span class="day-name">Thu</span>
                                <span class="day-date">18</span>
                            </div>
                            <div class="day-status late">
                                <i class="fas fa-clock"></i>
                                <span>7h 30m</span>
                            </div>
                        </div>
                        <div class="day-card today">
                            <div class="day-header">
                                <span class="day-name">Fri</span>
                                <span class="day-date">19</span>
                            </div>
                            <div class="day-status current">
                                <i class="fas fa-play"></i>
                                <span>In Progress</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Attendance Section -->
            <section id="attendance" class="content-section">
                <div class="section-header">
                    <h2>My Attendance Records</h2>
                    <div class="date-filter">
                        <input type="month" value="2024-01" id="monthFilter">
                        <button class="btn btn-primary">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                    </div>
                </div>
                
                <div class="attendance-stats">
                    <div class="stat-item">
                        <h4>Present Days</h4>
                        <span class="stat-value present">22</span>
                    </div>
                    <div class="stat-item">
                        <h4>Absent Days</h4>
                        <span class="stat-value absent">2</span>
                    </div>
                    <div class="stat-item">
                        <h4>Late Arrivals</h4>
                        <span class="stat-value late">3</span>
                    </div>
                    <div class="stat-item">
                        <h4>Attendance Rate</h4>
                        <span class="stat-value rate">91.7%</span>
                    </div>
                </div>

                <div class="attendance-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Check In</th>
                                <th>Check Out</th>
                                <th>Working Hours</th>
                                <th>Break Time</th>
                                <th>Status</th>
                                <th>Remarks</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Jan 19, 2024</td>
                                <td>09:00 AM</td>
                                <td>--:--</td>
                                <td>3h 45m</td>
                                <td>15m</td>
                                <td><span class="status present">Present</span></td>
                                <td>In Progress</td>
                            </tr>
                            <tr>
                                <td>Jan 18, 2024</td>
                                <td>09:15 AM</td>
                                <td>05:30 PM</td>
                                <td>7h 30m</td>
                                <td>45m</td>
                                <td><span class="status late">Late</span></td>
                                <td>Late arrival</td>
                            </tr>
                            <tr>
                                <td>Jan 17, 2024</td>
                                <td>08:45 AM</td>
                                <td>06:00 PM</td>
                                <td>8h 45m</td>
                                <td>30m</td>
                                <td><span class="status present">Present</span></td>
                                <td>Overtime</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Profile Section -->
            <section id="profile" class="content-section">
                <div class="profile-container">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=120&h=120&fit=crop&crop=face" alt="John Smith">
                            <button class="avatar-edit">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                        <div class="profile-info">
                            <h2>John Smith</h2>
                            <p>Software Developer</p>
                            <span class="employee-id">EMP001</span>
                        </div>
                    </div>
                    
                    <div class="profile-details">
                        <div class="detail-section">
                            <h3>Personal Information</h3>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <label>Email</label>
                                    <span><EMAIL></span>
                                </div>
                                <div class="detail-item">
                                    <label>Phone</label>
                                    <span>+****************</span>
                                </div>
                                <div class="detail-item">
                                    <label>Department</label>
                                    <span>Information Technology</span>
                                </div>
                                <div class="detail-item">
                                    <label>Join Date</label>
                                    <span>January 15, 2023</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-section">
                            <h3>Work Schedule</h3>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <label>Working Hours</label>
                                    <span>9:00 AM - 6:00 PM</span>
                                </div>
                                <div class="detail-item">
                                    <label>Working Days</label>
                                    <span>Monday - Friday</span>
                                </div>
                                <div class="detail-item">
                                    <label>Break Time</label>
                                    <span>1 hour</span>
                                </div>
                                <div class="detail-item">
                                    <label>Location</label>
                                    <span>Office - Floor 3</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <!-- Attendance Confirmation Modal -->
    <div class="modal hidden" id="attendanceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Confirm Check In</h3>
                <button class="modal-close" onclick="closeModal('attendanceModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="confirmation-info">
                    <div class="time-display">
                        <i class="fas fa-clock"></i>
                        <span id="modalTime"></span>
                    </div>
                    <p id="modalMessage">Are you sure you want to check in now?</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('attendanceModal')">Cancel</button>
                <button class="btn btn-primary" id="confirmBtn">Confirm</button>
            </div>
        </div>
    </div>

    <script src="attendance-script.js"></script>
</body>
</html>