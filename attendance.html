<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Attendance System</title>
    <link rel="stylesheet" href="attendance-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Main Login Container -->
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-clock"></i>
                <h1>Digital Attendance</h1>
            </div>
            <p>Streamlined attendance management system</p>
        </div>

        <!-- Login Type Selection -->
        <div class="login-type-selector">
            <button class="login-type-btn active" data-type="employee">
                <i class="fas fa-user"></i>
                <span>Employee Login</span>
            </button>
            <button class="login-type-btn" data-type="hr">
                <i class="fas fa-user-tie"></i>
                <span>HR Login</span>
            </button>
        </div>

        <!-- Employee Login Form -->
        <div class="login-form-container" id="employee-login">
            <form class="login-form" id="employeeLoginForm">
                <h2>Employee Login</h2>
                <div class="form-group">
                    <label for="emp-id">Employee ID</label>
                    <div class="input-group">
                        <i class="fas fa-id-badge"></i>
                        <input type="text" id="emp-id" name="empId" placeholder="Enter your Employee ID" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="emp-password">Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="emp-password" name="password" placeholder="Enter your password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('emp-password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" name="remember">
                        <span class="checkmark"></span>
                        Remember me
                    </label>
                    <a href="#" class="forgot-password">Forgot Password?</a>
                </div>
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    Login
                </button>
            </form>
        </div>

        <!-- HR Login Form -->
        <div class="login-form-container hidden" id="hr-login">
            <form class="login-form" id="hrLoginForm">
                <h2>HR Login</h2>
                <div class="form-group">
                    <label for="hr-username">Username</label>
                    <div class="input-group">
                        <i class="fas fa-user-tie"></i>
                        <input type="text" id="hr-username" name="username" placeholder="Enter HR username" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="hr-password">Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="hr-password" name="password" placeholder="Enter your password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('hr-password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="hr-department">Department</label>
                    <div class="input-group">
                        <i class="fas fa-building"></i>
                        <select id="hr-department" name="department" required>
                            <option value="">Select Department</option>
                            <option value="hr">Human Resources</option>
                            <option value="admin">Administration</option>
                            <option value="management">Management</option>
                        </select>
                    </div>
                </div>
                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" name="remember">
                        <span class="checkmark"></span>
                        Remember me
                    </label>
                    <a href="#" class="forgot-password">Forgot Password?</a>
                </div>
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    Login as HR
                </button>
            </form>
        </div>

        <!-- Quick Access Features -->
        <div class="quick-access">
            <h3>Quick Access</h3>
            <div class="quick-access-grid">
                <div class="quick-access-item">
                    <i class="fas fa-clock"></i>
                    <span>Real-time Tracking</span>
                </div>
                <div class="quick-access-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>Attendance Reports</span>
                </div>
                <div class="quick-access-item">
                    <i class="fas fa-mobile-alt"></i>
                    <span>Mobile Friendly</span>
                </div>
                <div class="quick-access-item">
                    <i class="fas fa-shield-alt"></i>
                    <span>Secure Access</span>
                </div>
            </div>
        </div>

        <!-- System Info -->
        <div class="system-info">
            <div class="current-time">
                <i class="fas fa-clock"></i>
                <span id="current-time"></span>
            </div>
            <div class="system-status">
                <i class="fas fa-circle online"></i>
                <span>System Online</span>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay hidden" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Authenticating...</p>
        </div>
    </div>

    <!-- Demo Credentials Info -->
    <div class="demo-info">
        <button class="demo-toggle" onclick="toggleDemoInfo()">
            <i class="fas fa-info-circle"></i>
            Demo Credentials
        </button>
        <div class="demo-content hidden" id="demoContent">
            <h4>Demo Login Credentials</h4>
            <div class="demo-section">
                <h5>Employee Login:</h5>
                <p><strong>Employee ID:</strong> EMP001</p>
                <p><strong>Password:</strong> employee123</p>
            </div>
            <div class="demo-section">
                <h5>HR Login:</h5>
                <p><strong>Username:</strong> hr_admin</p>
                <p><strong>Password:</strong> hr123</p>
                <p><strong>Department:</strong> Human Resources</p>
            </div>
        </div>
    </div>

    <script src="attendance-script.js"></script>
</body>
</html>
