/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c5aa0;
}

.nav-logo i {
    margin-right: 10px;
    font-size: 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
}

.nav-menu li {
    margin-left: 30px;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: #2c5aa0;
}

.dashboard-btn {
    background: #2c5aa0;
    color: white !important;
    padding: 10px 20px;
    border-radius: 5px;
    transition: background 0.3s;
}

.dashboard-btn:hover {
    background: #1e3f73;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    margin-bottom: 60px;
}

.btn {
    display: inline-block;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s;
    margin: 0 10px;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: #ff6b6b;
    color: white;
}

.btn-primary:hover {
    background: #ff5252;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #2c5aa0;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    max-width: 800px;
    margin: 0 auto;
}

.stat h3 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: #ff6b6b;
}

.stat p {
    font-size: 1rem;
    opacity: 0.9;
}

/* Services Section */
.services {
    padding: 80px 0;
    background: #f8f9fa;
}

.services h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #2c5aa0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background: white;
    padding: 40px 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-card i {
    font-size: 3rem;
    color: #ff6b6b;
    margin-bottom: 20px;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #2c5aa0;
}

.service-card p {
    color: #666;
    line-height: 1.6;
}

/* Departments Section */
.departments {
    padding: 80px 0;
}

.departments h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #2c5aa0;
}

.departments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.department-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.department-card:hover {
    transform: translateY(-5px);
}

.department-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.department-info {
    padding: 25px;
}

.department-info h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: #2c5aa0;
}

.department-info p {
    color: #666;
    margin-bottom: 15px;
}

.learn-more {
    color: #ff6b6b;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s;
}

.learn-more:hover {
    color: #ff5252;
}

/* Doctors Section */
.doctors {
    padding: 80px 0;
    background: #f8f9fa;
}

.doctors h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #2c5aa0;
}

.doctors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.doctor-card {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.doctor-card:hover {
    transform: translateY(-5px);
}

.doctor-card img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
}

.doctor-card h3 {
    font-size: 1.3rem;
    margin-bottom: 5px;
    color: #2c5aa0;
}

.doctor-card p {
    color: #ff6b6b;
    font-weight: 600;
    margin-bottom: 15px;
}

.doctor-info span {
    color: #666;
    font-size: 0.9rem;
    display: block;
    margin-bottom: 15px;
}

.doctor-contact i {
    color: #2c5aa0;
    margin: 0 10px;
    cursor: pointer;
    transition: color 0.3s;
}

.doctor-contact i:hover {
    color: #ff6b6b;
}

/* Contact Section */
.contact {
    padding: 80px 0;
}

.contact h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #2c5aa0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
}

.contact-item i {
    font-size: 1.5rem;
    color: #ff6b6b;
    margin-right: 20px;
    margin-top: 5px;
}

.contact-item h4 {
    color: #2c5aa0;
    margin-bottom: 5px;
}

.contact-item p {
    color: #666;
    margin: 2px 0;
}

.appointment-form {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 10px;
}

.appointment-form h3 {
    color: #2c5aa0;
    margin-bottom: 30px;
    text-align: center;
}

.appointment-form input,
.appointment-form select,
.appointment-form textarea {
    width: 100%;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.appointment-form textarea {
    height: 100px;
    resize: vertical;
}

/* Footer */
.footer {
    background: #2c5aa0;
    color: white;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 20px;
}

.footer-section p {
    margin-bottom: 10px;
    opacity: 0.9;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: white;
    text-decoration: none;
    opacity: 0.9;
    transition: opacity 0.3s;
}

.footer-section ul li a:hover {
    opacity: 1;
}

.social-links a {
    color: white;
    font-size: 1.5rem;
    margin-right: 15px;
    transition: color 0.3s;
}

.social-links a:hover {
    color: #ff6b6b;
}

.emergency-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ff6b6b;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255,255,255,0.2);
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 20px 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu li {
        margin: 15px 0;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}
