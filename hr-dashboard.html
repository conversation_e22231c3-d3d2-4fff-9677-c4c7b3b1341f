<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HR Dashboard - Digital Attendance</title>
    <link rel="stylesheet" href="attendance-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="dashboard-body">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-clock"></i>
                <h3>HR Dashboard</h3>
            </div>
        </div>
        <nav class="sidebar-nav">
            <a href="#overview" class="nav-item active">
                <i class="fas fa-tachometer-alt"></i>
                <span>Overview</span>
            </a>
            <a href="#employees" class="nav-item">
                <i class="fas fa-users"></i>
                <span>Employees</span>
            </a>
            <a href="#attendance" class="nav-item">
                <i class="fas fa-calendar-check"></i>
                <span>Attendance</span>
            </a>
            <a href="#reports" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span>Reports</span>
            </a>
            <a href="#departments" class="nav-item">
                <i class="fas fa-building"></i>
                <span>Departments</span>
            </a>
            <a href="#settings" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
        <div class="sidebar-footer">
            <a href="attendance.html" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>HR Dashboard</h1>
            </div>
            <div class="header-right">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search employees...">
                </div>
                <div class="notifications">
                    <i class="fas fa-bell"></i>
                    <span class="notification-count">5</span>
                </div>
                <div class="user-profile">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="HR Admin">
                    <span>HR Admin</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <div class="dashboard-content">
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon employees">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3>156</h3>
                            <p>Total Employees</p>
                            <span class="stat-change positive">+3 this month</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon present">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-info">
                            <h3>142</h3>
                            <p>Present Today</p>
                            <span class="stat-change positive">91% attendance</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon absent">
                            <i class="fas fa-user-times"></i>
                        </div>
                        <div class="stat-info">
                            <h3>14</h3>
                            <p>Absent Today</p>
                            <span class="stat-change negative">9% absence</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon late">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3>8</h3>
                            <p>Late Arrivals</p>
                            <span class="stat-change neutral">5% late</span>
                        </div>
                    </div>
                </div>

                <!-- Charts and Tables -->
                <div class="dashboard-grid">
                    <div class="chart-container">
                        <h3>Attendance Trends</h3>
                        <canvas id="attendanceChart"></canvas>
                    </div>
                    <div class="recent-activity">
                        <h3>Recent Activity</h3>
                        <div class="activity-list">
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-sign-in-alt"></i>
                                </div>
                                <div class="activity-info">
                                    <p><strong>John Smith</strong> checked in</p>
                                    <span>2 minutes ago</span>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-sign-out-alt"></i>
                                </div>
                                <div class="activity-info">
                                    <p><strong>Sarah Wilson</strong> checked out</p>
                                    <span>15 minutes ago</span>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-info">
                                    <p><strong>New employee</strong> added</p>
                                    <span>1 hour ago</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Today's Attendance Table -->
                <div class="attendance-table">
                    <div class="table-header">
                        <h3>Today's Attendance</h3>
                        <div class="table-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-filter"></i>
                                Filter
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Employee ID</th>
                                    <th>Name</th>
                                    <th>Department</th>
                                    <th>Check In</th>
                                    <th>Check Out</th>
                                    <th>Status</th>
                                    <th>Working Hours</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="attendanceTableBody">
                                <tr>
                                    <td>EMP001</td>
                                    <td>John Smith</td>
                                    <td>IT</td>
                                    <td>09:00 AM</td>
                                    <td>--:--</td>
                                    <td><span class="status present">Present</span></td>
                                    <td>3h 45m</td>
                                    <td>
                                        <button class="btn-small">View</button>
                                        <button class="btn-small">Edit</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>EMP002</td>
                                    <td>Sarah Wilson</td>
                                    <td>HR</td>
                                    <td>08:45 AM</td>
                                    <td>05:30 PM</td>
                                    <td><span class="status completed">Completed</span></td>
                                    <td>8h 45m</td>
                                    <td>
                                        <button class="btn-small">View</button>
                                        <button class="btn-small">Edit</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>EMP003</td>
                                    <td>Mike Johnson</td>
                                    <td>Finance</td>
                                    <td>--:--</td>
                                    <td>--:--</td>
                                    <td><span class="status absent">Absent</span></td>
                                    <td>0h 0m</td>
                                    <td>
                                        <button class="btn-small">View</button>
                                        <button class="btn-small">Edit</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Employees Section -->
            <section id="employees" class="content-section">
                <div class="section-header">
                    <h2>Employee Management</h2>
                    <button class="btn btn-primary" onclick="showAddEmployeeModal()">
                        <i class="fas fa-user-plus"></i>
                        Add Employee
                    </button>
                </div>
                
                <div class="employees-grid">
                    <div class="employee-card">
                        <div class="employee-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face" alt="John Smith">
                        </div>
                        <div class="employee-info">
                            <h4>John Smith</h4>
                            <p>Software Developer</p>
                            <span class="employee-id">EMP001</span>
                        </div>
                        <div class="employee-status">
                            <span class="status present">Present</span>
                        </div>
                        <div class="employee-actions">
                            <button class="btn-icon" title="View Profile">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn-icon" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="employee-card">
                        <div class="employee-avatar">
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=80&h=80&fit=crop&crop=face" alt="Sarah Wilson">
                        </div>
                        <div class="employee-info">
                            <h4>Sarah Wilson</h4>
                            <p>HR Manager</p>
                            <span class="employee-id">EMP002</span>
                        </div>
                        <div class="employee-status">
                            <span class="status completed">Completed</span>
                        </div>
                        <div class="employee-actions">
                            <button class="btn-icon" title="View Profile">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn-icon" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <!-- Add Employee Modal -->
    <div class="modal hidden" id="addEmployeeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Employee</h3>
                <button class="modal-close" onclick="closeModal('addEmployeeModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="modal-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>Employee ID</label>
                        <input type="text" placeholder="EMP001" required>
                    </div>
                    <div class="form-group">
                        <label>Full Name</label>
                        <input type="text" placeholder="John Doe" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" placeholder="<EMAIL>" required>
                    </div>
                    <div class="form-group">
                        <label>Phone</label>
                        <input type="tel" placeholder="+1234567890" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Department</label>
                        <select required>
                            <option value="">Select Department</option>
                            <option value="IT">IT</option>
                            <option value="HR">HR</option>
                            <option value="Finance">Finance</option>
                            <option value="Marketing">Marketing</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Position</label>
                        <input type="text" placeholder="Software Developer" required>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addEmployeeModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Employee</button>
                </div>
            </form>
        </div>
    </div>

    <script src="attendance-script.js"></script>
</body>
</html>
