<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excellence College - Courses & Fee Structure</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* Navigation */
        .nav {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav li {
            margin: 0 1rem;
        }

        .nav a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav a:hover {
            background: #667eea;
            color: white;
        }

        /* Main Content */
        .main-content {
            padding: 3rem 0;
        }

        .section {
            background: white;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .section-header h2 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .section-content {
            padding: 2rem;
        }

        /* Course Cards */
        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .course-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .course-card:hover {
            transform: translateY(-5px);
        }

        .course-header {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .course-header h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .course-duration {
            background: rgba(255,255,255,0.2);
            padding: 0.3rem 1rem;
            border-radius: 20px;
            display: inline-block;
            font-size: 0.9rem;
        }

        .course-details {
            padding: 1.5rem;
        }

        .course-details h4 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .course-details ul {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .course-details li {
            padding: 0.3rem 0;
            position: relative;
            padding-left: 1.5rem;
        }

        .course-details li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }

        /* Fee Structure Table */
        .fee-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .fee-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .fee-table td {
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }

        .fee-table tr:hover {
            background: #f8f9fa;
        }

        .fee-amount {
            font-weight: bold;
            color: #667eea;
        }

        .total-row {
            background: #f8f9fa;
            font-weight: bold;
        }

        .total-row td {
            border-top: 2px solid #667eea;
        }

        /* Admission Info */
        .admission-info {
            background: linear-gradient(135deg, #ffecd2, #fcb69f);
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
        }

        .admission-info h3 {
            color: #d63384;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .info-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .info-item h4 {
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        /* Contact Section */
        .contact-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .contact-item {
            background: rgba(255,255,255,0.1);
            padding: 1.5rem;
            border-radius: 10px;
        }

        .contact-item i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #ffd700;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .nav ul {
                flex-direction: column;
                align-items: center;
            }

            .nav li {
                margin: 0.5rem 0;
            }

            .courses-grid {
                grid-template-columns: 1fr;
            }

            .fee-table {
                font-size: 0.9rem;
            }

            .fee-table th,
            .fee-table td {
                padding: 0.5rem;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 0.8rem 2rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2, #fcb69f);
            color: #333;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1>Excellence College of Technology</h1>
            <p>Empowering Future Leaders Through Quality Education</p>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="#courses">Courses</a></li>
                <li><a href="#fees">Fee Structure</a></li>
                <li><a href="#admission">Admission</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Courses Section -->
            <section id="courses" class="section">
                <div class="section-header">
                    <h2>Our Courses</h2>
                    <p>Choose from our comprehensive range of undergraduate programs</p>
                </div>
                <div class="section-content">
                    <div class="courses-grid">
                        <!-- Computer Science Engineering -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Computer Science Engineering</h3>
                                <span class="course-duration">4 Years (B.Tech)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Programming Languages (C, C++, Java, Python)</li>
                                    <li>Data Structures & Algorithms</li>
                                    <li>Database Management Systems</li>
                                    <li>Web Development & Mobile Apps</li>
                                    <li>Machine Learning & AI</li>
                                    <li>Software Engineering</li>
                                    <li>Cybersecurity Fundamentals</li>
                                    <li>Industry Internships</li>
                                </ul>
                                <h4>Career Opportunities</h4>
                                <ul>
                                    <li>Software Developer</li>
                                    <li>Data Scientist</li>
                                    <li>System Analyst</li>
                                    <li>Cybersecurity Specialist</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Mechanical Engineering -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Mechanical Engineering</h3>
                                <span class="course-duration">4 Years (B.Tech)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Engineering Mechanics</li>
                                    <li>Thermodynamics</li>
                                    <li>Machine Design</li>
                                    <li>Manufacturing Processes</li>
                                    <li>CAD/CAM</li>
                                    <li>Robotics & Automation</li>
                                    <li>Quality Control</li>
                                    <li>Industrial Training</li>
                                </ul>
                                <h4>Career Opportunities</h4>
                                <ul>
                                    <li>Design Engineer</li>
                                    <li>Production Manager</li>
                                    <li>Quality Assurance Engineer</li>
                                    <li>Research & Development</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Electrical Engineering -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Electrical Engineering</h3>
                                <span class="course-duration">4 Years (B.Tech)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Circuit Analysis</li>
                                    <li>Power Systems</li>
                                    <li>Control Systems</li>
                                    <li>Electronics & Communication</li>
                                    <li>Renewable Energy</li>
                                    <li>Electric Machines</li>
                                    <li>Power Electronics</li>
                                    <li>Project Work</li>
                                </ul>
                                <h4>Career Opportunities</h4>
                                <ul>
                                    <li>Electrical Engineer</li>
                                    <li>Power System Engineer</li>
                                    <li>Control System Engineer</li>
                                    <li>Renewable Energy Specialist</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Civil Engineering -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Civil Engineering</h3>
                                <span class="course-duration">4 Years (B.Tech)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Structural Engineering</li>
                                    <li>Environmental Engineering</li>
                                    <li>Transportation Engineering</li>
                                    <li>Geotechnical Engineering</li>
                                    <li>Construction Management</li>
                                    <li>Building Information Modeling</li>
                                    <li>Surveying & GIS</li>
                                    <li>Site Visits & Practical Training</li>
                                </ul>
                                <h4>Career Opportunities</h4>
                                <ul>
                                    <li>Structural Engineer</li>
                                    <li>Project Manager</li>
                                    <li>Construction Engineer</li>
                                    <li>Urban Planner</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Business Administration -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Business Administration</h3>
                                <span class="course-duration">3 Years (BBA)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Business Management</li>
                                    <li>Marketing & Sales</li>
                                    <li>Financial Management</li>
                                    <li>Human Resource Management</li>
                                    <li>Operations Management</li>
                                    <li>Business Analytics</li>
                                    <li>Entrepreneurship</li>
                                    <li>Industry Exposure</li>
                                </ul>
                                <h4>Career Opportunities</h4>
                                <ul>
                                    <li>Business Manager</li>
                                    <li>Marketing Executive</li>
                                    <li>Financial Analyst</li>
                                    <li>HR Specialist</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Commerce -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Commerce</h3>
                                <span class="course-duration">3 Years (B.Com)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Accounting & Auditing</li>
                                    <li>Banking & Finance</li>
                                    <li>Taxation</li>
                                    <li>Business Law</li>
                                    <li>Economics</li>
                                    <li>Computer Applications</li>
                                    <li>Business Communication</li>
                                    <li>Practical Training</li>
                                </ul>
                                <h4>Career Opportunities</h4>
                                <ul>
                                    <li>Chartered Accountant</li>
                                    <li>Banking Professional</li>
                                    <li>Tax Consultant</li>
                                    <li>Financial Advisor</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Fee Structure Section -->
            <section id="fees" class="section">
                <div class="section-header">
                    <h2>Fee Structure (3 Years)</h2>
                    <p>Transparent and affordable fee structure for all courses</p>
                </div>
                <div class="section-content">
                    <!-- Engineering Courses Fee Structure -->
                    <h3 style="color: #667eea; margin-bottom: 1rem; font-size: 1.5rem;">Engineering Courses (B.Tech - 4 Years)</h3>
                    <table class="fee-table">
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>Year 1</th>
                                <th>Year 2</th>
                                <th>Year 3</th>
                                <th>Year 4</th>
                                <th>Total (4 Years)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Computer Science Engineering</strong></td>
                                <td class="fee-amount">₹1,50,000</td>
                                <td class="fee-amount">₹1,55,000</td>
                                <td class="fee-amount">₹1,60,000</td>
                                <td class="fee-amount">₹1,65,000</td>
                                <td class="fee-amount"><strong>₹6,30,000</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Mechanical Engineering</strong></td>
                                <td class="fee-amount">₹1,40,000</td>
                                <td class="fee-amount">₹1,45,000</td>
                                <td class="fee-amount">₹1,50,000</td>
                                <td class="fee-amount">₹1,55,000</td>
                                <td class="fee-amount"><strong>₹5,90,000</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Electrical Engineering</strong></td>
                                <td class="fee-amount">₹1,35,000</td>
                                <td class="fee-amount">₹1,40,000</td>
                                <td class="fee-amount">₹1,45,000</td>
                                <td class="fee-amount">₹1,50,000</td>
                                <td class="fee-amount"><strong>₹5,70,000</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Civil Engineering</strong></td>
                                <td class="fee-amount">₹1,30,000</td>
                                <td class="fee-amount">₹1,35,000</td>
                                <td class="fee-amount">₹1,40,000</td>
                                <td class="fee-amount">₹1,45,000</td>
                                <td class="fee-amount"><strong>₹5,50,000</strong></td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Management & Commerce Courses Fee Structure -->
                    <h3 style="color: #667eea; margin: 2rem 0 1rem 0; font-size: 1.5rem;">Management & Commerce Courses (3 Years)</h3>
                    <table class="fee-table">
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>Year 1</th>
                                <th>Year 2</th>
                                <th>Year 3</th>
                                <th>Total (3 Years)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Business Administration (BBA)</strong></td>
                                <td class="fee-amount">₹80,000</td>
                                <td class="fee-amount">₹85,000</td>
                                <td class="fee-amount">₹90,000</td>
                                <td class="fee-amount"><strong>₹2,55,000</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Commerce (B.Com)</strong></td>
                                <td class="fee-amount">₹60,000</td>
                                <td class="fee-amount">₹65,000</td>
                                <td class="fee-amount">₹70,000</td>
                                <td class="fee-amount"><strong>₹1,95,000</strong></td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Fee Components Breakdown -->
                    <div style="margin-top: 2rem; background: #f8f9fa; padding: 1.5rem; border-radius: 10px;">
                        <h4 style="color: #667eea; margin-bottom: 1rem;">Fee Components Include:</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                            <div>
                                <h5 style="color: #333; margin-bottom: 0.5rem;">Academic Fees</h5>
                                <ul style="list-style: none; padding-left: 0;">
                                    <li>✓ Tuition Fees</li>
                                    <li>✓ Laboratory Fees</li>
                                    <li>✓ Library Fees</li>
                                    <li>✓ Examination Fees</li>
                                </ul>
                            </div>
                            <div>
                                <h5 style="color: #333; margin-bottom: 0.5rem;">Infrastructure Fees</h5>
                                <ul style="list-style: none; padding-left: 0;">
                                    <li>✓ Development Fees</li>
                                    <li>✓ Maintenance Fees</li>
                                    <li>✓ Sports & Recreation</li>
                                    <li>✓ Technology Fees</li>
                                </ul>
                            </div>
                            <div>
                                <h5 style="color: #333; margin-bottom: 0.5rem;">Additional Services</h5>
                                <ul style="list-style: none; padding-left: 0;">
                                    <li>✓ Student Activities</li>
                                    <li>✓ Placement Support</li>
                                    <li>✓ Industry Training</li>
                                    <li>✓ Career Counseling</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Options -->
                    <div style="margin-top: 2rem; background: linear-gradient(135deg, #e3f2fd, #bbdefb); padding: 1.5rem; border-radius: 10px;">
                        <h4 style="color: #1976d2; margin-bottom: 1rem;">Payment Options & Scholarships</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                            <div style="background: white; padding: 1rem; border-radius: 8px;">
                                <h5 style="color: #667eea;">Flexible Payment Plans</h5>
                                <ul style="margin-top: 0.5rem;">
                                    <li>Annual Payment (5% discount)</li>
                                    <li>Semester-wise Payment</li>
                                    <li>Monthly Installments (EMI)</li>
                                    <li>Education Loan Assistance</li>
                                </ul>
                            </div>
                            <div style="background: white; padding: 1rem; border-radius: 8px;">
                                <h5 style="color: #667eea;">Scholarship Programs</h5>
                                <ul style="margin-top: 0.5rem;">
                                    <li>Merit-based Scholarships (up to 50%)</li>
                                    <li>Need-based Financial Aid</li>
                                    <li>Sports & Cultural Scholarships</li>
                                    <li>Alumni Referral Benefits</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Admission Information Section -->
            <section id="admission" class="section">
                <div class="section-header">
                    <h2>Admission Information</h2>
                    <p>Your pathway to excellence starts here</p>
                </div>
                <div class="section-content">
                    <div class="admission-info">
                        <h3>Admission Process & Requirements</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <h4>Eligibility Criteria</h4>
                                <p><strong>Engineering:</strong> 12th with PCM (60% minimum)<br>
                                <strong>BBA:</strong> 12th from any stream (50% minimum)<br>
                                <strong>B.Com:</strong> 12th with Commerce/Science (50% minimum)</p>
                            </div>
                            <div class="info-item">
                                <h4>Entrance Exams</h4>
                                <p><strong>Engineering:</strong> JEE Main/State CET<br>
                                <strong>Management:</strong> College Entrance Test<br>
                                <strong>Commerce:</strong> Merit-based admission</p>
                            </div>
                            <div class="info-item">
                                <h4>Important Dates</h4>
                                <p><strong>Application Start:</strong> March 1st<br>
                                <strong>Application Deadline:</strong> June 30th<br>
                                <strong>Admission Process:</strong> July 1-31st<br>
                                <strong>Classes Begin:</strong> August 1st</p>
                            </div>
                            <div class="info-item">
                                <h4>Required Documents</h4>
                                <p>• 10th & 12th Mark Sheets<br>
                                • Transfer Certificate<br>
                                • Character Certificate<br>
                                • Entrance Exam Scorecard<br>
                                • Passport Size Photos<br>
                                • Caste Certificate (if applicable)</p>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 2rem;">
                            <a href="#contact" class="btn">Apply Now</a>
                            <a href="#" class="btn btn-secondary" style="margin-left: 1rem;">Download Brochure</a>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="container">
            <h2>Contact Us</h2>
            <p>Get in touch with us for admissions and inquiries</p>

            <div class="contact-grid">
                <div class="contact-item">
                    <i>📍</i>
                    <h3>Address</h3>
                    <p>Excellence College Campus<br>
                    123 Education Street<br>
                    Knowledge City, State - 123456</p>
                </div>
                <div class="contact-item">
                    <i>📞</i>
                    <h3>Phone</h3>
                    <p>Admissions: +91 98765 43210<br>
                    General: +91 98765 43211<br>
                    Toll Free: 1800-123-4567</p>
                </div>
                <div class="contact-item">
                    <i>✉️</i>
                    <h3>Email</h3>
                    <p><EMAIL><br>
                    <EMAIL><br>
                    <EMAIL></p>
                </div>
                <div class="contact-item">
                    <i>🌐</i>
                    <h3>Website & Social</h3>
                    <p>www.excellencecollege.edu<br>
                    Facebook: @ExcellenceCollege<br>
                    Instagram: @excellence_college</p>
                </div>
            </div>

            <div style="margin-top: 2rem;">
                <a href="tel:+919876543210" class="btn">Call Now</a>
                <a href="mailto:<EMAIL>" class="btn btn-secondary" style="margin-left: 1rem;">Email Us</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: #333; color: white; text-align: center; padding: 2rem 0;">
        <div class="container">
            <p>&copy; 2024 Excellence College of Technology. All rights reserved.</p>
            <p style="margin-top: 0.5rem; opacity: 0.8;">Empowering students to achieve excellence in education and career</p>
        </div>
    </footer>

    <script>
        // Simple JavaScript for smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.course-card, .fee-table, .info-item').forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'all 0.6s ease';
            observer.observe(element);
        });

        // Add hover effects to tables
        document.querySelectorAll('.fee-table tr').forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#f0f8ff';
            });
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
    </script>
</body>
</html>
