// CodeCraft Solutions Website JavaScript

// Global variables
let jobApplications = [];
let currentFilter = 'all';

// Initialize the website
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
});

function initializeWebsite() {
    setupNavigation();
    setupPortfolioFilters();
    setupJobFilters();
    setupFormHandling();
    setupAnimations();
    loadJobApplications();
}

// Navigation functionality
function setupNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // Close mobile menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-menu a');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }
    
    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Navbar background change on scroll
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            }
        });
    }
}

// Portfolio filters
function setupPortfolioFilters() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');
    
    if (filterBtns.length === 0) return;
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Filter portfolio items
            portfolioItems.forEach(item => {
                const category = item.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    item.style.display = 'block';
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'scale(1)';
                    }, 100);
                } else {
                    item.style.opacity = '0';
                    item.style.transform = 'scale(0.8)';
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
        });
    });
}

// Job filters for careers page
function setupJobFilters() {
    const departmentFilter = document.getElementById('departmentFilter');
    const locationFilter = document.getElementById('locationFilter');
    const typeFilter = document.getElementById('typeFilter');
    
    if (!departmentFilter) return;
    
    [departmentFilter, locationFilter, typeFilter].forEach(filter => {
        filter.addEventListener('change', filterJobs);
    });
}

function filterJobs() {
    const departmentFilter = document.getElementById('departmentFilter').value;
    const locationFilter = document.getElementById('locationFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    
    const jobCards = document.querySelectorAll('.job-card');
    
    jobCards.forEach(card => {
        const department = card.getAttribute('data-department');
        const location = card.getAttribute('data-location');
        const type = card.getAttribute('data-type');
        
        const departmentMatch = departmentFilter === 'all' || department === departmentFilter;
        const locationMatch = locationFilter === 'all' || location === locationFilter;
        const typeMatch = typeFilter === 'all' || type === typeFilter;
        
        if (departmentMatch && locationMatch && typeMatch) {
            card.style.display = 'block';
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        } else {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.display = 'none';
            }, 300);
        }
    });
}

// Form handling
function setupFormHandling() {
    const contactForm = document.getElementById('contactForm');
    const jobApplicationForm = document.getElementById('jobApplicationForm');
    
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
    }
    
    if (jobApplicationForm) {
        jobApplicationForm.addEventListener('submit', handleJobApplication);
    }
    
    // Newsletter signup
    const newsletterForm = document.querySelector('.newsletter-signup');
    if (newsletterForm) {
        const button = newsletterForm.querySelector('button');
        if (button) {
            button.addEventListener('click', handleNewsletterSignup);
        }
    }
}

function handleContactForm(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const contactData = {
        name: formData.get('name'),
        email: formData.get('email'),
        company: formData.get('company'),
        budget: formData.get('budget'),
        service: formData.get('service'),
        message: formData.get('message')
    };
    
    // Validate form data
    if (!validateContactData(contactData)) {
        showNotification('Please fill in all required fields.', 'error');
        return;
    }
    
    // Show loading state
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Sending...';
    submitButton.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Reset form
        e.target.reset();
        
        // Reset button
        submitButton.textContent = originalText;
        submitButton.disabled = false;
        
        // Show success message
        showNotification('Message sent successfully! We will contact you soon.', 'success');
        
        console.log('Contact form data:', contactData);
    }, 2000);
}

function handleJobApplication(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const applicationData = {
        firstName: formData.get('firstName'),
        lastName: formData.get('lastName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        location: formData.get('location'),
        experience: formData.get('experience'),
        currentRole: formData.get('currentRole'),
        currentCompany: formData.get('currentCompany'),
        expectedSalary: formData.get('expectedSalary'),
        portfolio: formData.get('portfolio'),
        linkedin: formData.get('linkedin'),
        github: formData.get('github'),
        coverLetter: formData.get('coverLetter'),
        availability: formData.get('availability'),
        workAuthorization: formData.get('workAuthorization'),
        resume: formData.get('resume'),
        jobTitle: document.getElementById('modalJobTitle').textContent.replace('Apply for ', ''),
        submittedAt: new Date().toISOString(),
        status: 'pending'
    };
    
    // Validate form data
    if (!validateJobApplication(applicationData)) {
        showNotification('Please fill in all required fields.', 'error');
        return;
    }
    
    // Show loading state
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Submitting...';
    submitButton.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Add to applications array
        applicationData.id = Date.now();
        jobApplications.push(applicationData);
        
        // Store in localStorage for demo purposes
        localStorage.setItem('jobApplications', JSON.stringify(jobApplications));
        
        // Reset form
        e.target.reset();
        
        // Reset button
        submitButton.textContent = originalText;
        submitButton.disabled = false;
        
        // Close modal
        closeJobModal();
        
        // Show success message
        showNotification('Application submitted successfully! We will review your application and contact you soon.', 'success');
        
        console.log('Job application data:', applicationData);
    }, 3000);
}

function handleNewsletterSignup(e) {
    e.preventDefault();
    
    const input = e.target.parentElement.querySelector('input');
    const email = input.value.trim();
    
    if (!email || !isValidEmail(email)) {
        showNotification('Please enter a valid email address.', 'error');
        return;
    }
    
    // Show loading state
    const originalText = e.target.textContent;
    e.target.textContent = 'Subscribing...';
    e.target.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Reset
        input.value = '';
        e.target.textContent = originalText;
        e.target.disabled = false;
        
        // Show success message
        showNotification('Successfully subscribed to our newsletter!', 'success');
    }, 1500);
}

function validateContactData(data) {
    return data.name && data.email && data.service && data.message && isValidEmail(data.email);
}

function validateJobApplication(data) {
    return data.firstName && data.lastName && data.email && data.phone && 
           data.location && data.experience && data.workAuthorization && 
           data.resume && isValidEmail(data.email);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Job modal functionality
function openJobModal(jobId) {
    const modal = document.getElementById('jobModal');
    const modalTitle = document.getElementById('modalJobTitle');
    
    // Set job title based on jobId
    const jobTitles = {
        'senior-fullstack-developer': 'Senior Full Stack Developer',
        'senior-ux-ui-designer': 'Senior UX/UI Designer',
        'devops-engineer': 'DevOps Engineer',
        'senior-product-manager': 'Senior Product Manager',
        'frontend-developer': 'Frontend Developer',
        'data-scientist': 'Data Scientist',
        'digital-marketing-manager': 'Digital Marketing Manager',
        'software-engineering-intern': 'Software Engineering Intern'
    };
    
    modalTitle.textContent = `Apply for ${jobTitles[jobId] || 'Position'}`;
    
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }
}

function closeJobModal() {
    const modal = document.getElementById('jobModal');
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
        
        // Reset form
        const form = document.getElementById('jobApplicationForm');
        if (form) {
            form.reset();
        }
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('jobModal');
    if (modal && e.target === modal) {
        closeJobModal();
    }
});

// Load job applications from localStorage
function loadJobApplications() {
    const stored = localStorage.getItem('jobApplications');
    if (stored) {
        try {
            jobApplications = JSON.parse(stored);
        } catch (e) {
            console.error('Error loading job applications:', e);
            jobApplications = [];
        }
    }
}

// Animations
function setupAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.service-card, .portfolio-item, .team-member, .benefit-card, .job-card, .testimonial-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });
    
    // Counter animation for stats
    animateCounters();
}

function animateCounters() {
    const stats = document.querySelectorAll('.stat-item h3');
    let animated = false;
    
    const animateNumbers = () => {
        if (animated) return;
        
        stats.forEach(stat => {
            const target = parseInt(stat.textContent.replace(/[^0-9]/g, ''));
            const increment = target / 100;
            let current = 0;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                
                const originalText = stat.textContent;
                if (originalText.includes('+')) {
                    stat.textContent = Math.floor(current) + '+';
                } else if (originalText.includes('%')) {
                    stat.textContent = Math.floor(current) + '%';
                } else if (originalText.includes('/')) {
                    stat.textContent = Math.floor(current * 10) / 10 + '/5';
                } else {
                    stat.textContent = Math.floor(current);
                }
            }, 20);
        });
        
        animated = true;
    };
    
    // Trigger animation when stats section is visible
    const statsSection = document.querySelector('.hero-stats');
    if (statsSection) {
        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateNumbers();
                }
            });
        }, { threshold: 0.5 });
        
        statsObserver.observe(statsSection);
    }
}

// Utility functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // Add styles if not already present
    if (!document.querySelector('.notification-styles')) {
        const style = document.createElement('style');
        style.className = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            }
            
            .notification.show {
                opacity: 1;
                transform: translateX(0);
            }
            
            .notification-content {
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                display: flex;
                align-items: center;
                justify-content: space-between;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }
            
            .notification-success .notification-content {
                background: #27ae60;
            }
            
            .notification-error .notification-content {
                background: #e74c3c;
            }
            
            .notification-info .notification-content {
                background: #3498db;
            }
            
            .notification-close {
                background: none;
                border: none;
                color: white;
                font-size: 1.2rem;
                cursor: pointer;
                margin-left: 15px;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Add to page
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    // Auto hide after 5 seconds
    setTimeout(() => {
        hideNotification(notification);
    }, 5000);
    
    // Close button functionality
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        hideNotification(notification);
    });
}

function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// Export functions for global access
window.codecraftSolutions = {
    openJobModal,
    closeJobModal,
    showNotification,
    filterJobs
};
