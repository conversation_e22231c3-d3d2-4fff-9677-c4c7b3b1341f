<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Attendance System</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            background-color: #ffffff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 100%;
            max-width: 900px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        .form-group {
            margin-bottom: 15px;
            width: 100%;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        .form-group input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }
        .btn {
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            text-align: center;
            width: 100%;
        }
        .btn-primary {
            background-color: #4f46e5;
            color: white;
            border: none;
        }
        .btn-primary:hover {
            background-color: #4338ca;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background-color: #e0e7ff;
            color: #4f46e5;
            border: 1px solid #4f46e5;
        }
        .btn-secondary:hover {
            background-color: #c7d2fe;
            transform: translateY(-2px);
        }
        .table-container {
            width: 100%;
            overflow-x: auto;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background-color: #f9fafb;
            border-radius: 10px;
            overflow: hidden;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        th {
            background-color: #eff6ff;
            font-weight: 600;
            color: #1f2937;
            text-transform: uppercase;
            font-size: 0.85rem;
        }
        tr:last-child td {
            border-bottom: none;
        }
        .hidden {
            display: none;
        }
        .message-box {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            pointer-events: none;
        }
        .message-box.show {
            opacity: 1;
            pointer-events: auto;
        }
        @media (min-width: 640px) {
            .btn {
                width: auto;
            }
            .btn-group {
                display: flex;
                gap: 15px;
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-3xl font-bold text-gray-800 mb-6">Digital Attendance System</h1>

        <!-- Message Box -->
        <div id="messageBox" class="message-box"></div>

        <!-- Login View -->
        <div id="login-view" class="w-full max-w-md">
            <h2 class="text-2xl font-semibold text-gray-700 mb-6 text-center">Login</h2>
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" placeholder="Enter your email" class="focus:ring-2 focus:ring-indigo-200">
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter your password" class="focus:ring-2 focus:ring-indigo-200">
            </div>
            <div class="btn-group">
                <button id="employee-login-btn" class="btn btn-primary">Employee Login</button>
                <button id="hr-login-btn" class="btn btn-secondary">HR Login</button>
            </div>
        </div>

        <!-- Employee Dashboard View -->
        <div id="employee-dashboard-view" class="w-full hidden">
            <h2 class="text-2xl font-semibold text-gray-700 mb-4">Employee Dashboard</h2>
            <p class="text-gray-600 mb-4">Welcome, <span id="employee-name" class="font-medium"></span>! Your User ID: <span id="employee-user-id" class="font-medium text-blue-600"></span></p>
            <div class="flex flex-col sm:flex-row gap-4 mb-6">
                <button id="mark-attendance-btn" class="btn btn-primary flex-grow">Mark My Attendance</button>
                <button id="employee-logout-btn" class="btn btn-secondary flex-grow">Logout</button>
            </div>

            <h3 class="text-xl font-semibold text-gray-700 mb-3">Your Attendance History</h3>
            <div class="table-container">
                <table id="employee-attendance-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Time</th>
                            <th>Type</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Attendance records will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- HR Dashboard View -->
        <div id="hr-dashboard-view" class="w-full hidden">
            <h2 class="text-2xl font-semibold text-gray-700 mb-4">HR Dashboard</h2>
            <p class="text-gray-600 mb-4">Welcome, HR! Your User ID: <span id="hr-user-id" class="font-medium text-blue-600"></span></p>
            <button id="hr-logout-btn" class="btn btn-secondary mb-6 w-full sm:w-auto">Logout</button>

            <h3 class="text-xl font-semibold text-gray-700 mb-3">Attendance Statistics</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 p-6 rounded-lg shadow-sm">
                    <p class="text-sm font-medium text-blue-600">Total Employees</p>
                    <p id="stat-total-employees" class="text-3xl font-bold text-blue-800 mt-1">0</p>
                </div>
                <div class="bg-green-50 p-6 rounded-lg shadow-sm">
                    <p class="text-sm font-medium text-green-600">Total Attendance Entries</p>
                    <p id="stat-total-entries" class="text-3xl font-bold text-green-800 mt-1">0</p>
                </div>
                <div class="bg-yellow-50 p-6 rounded-lg shadow-sm">
                    <p class="text-sm font-medium text-yellow-600">Unique Users Clocked In Today</p>
                    <p id="stat-today-unique" class="text-3xl font-bold text-yellow-800 mt-1">0</p>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-700 mb-3">All Attendance Records</h3>
            <div class="table-container">
                <table id="all-attendance-table">
                    <thead>
                        <tr>
                            <th>User ID</th>
                            <th>User Name</th>
                            <th>Date</th>
                            <th>Time</th>
                            <th>Type</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- All attendance records will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Firebase SDKs -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInWithEmailAndPassword, signOut, onAuthStateChanged, signInAnonymously, signInWithCustomToken } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, addDoc, setDoc, updateDoc, deleteDoc, onSnapshot, collection, query, where, getDocs, serverTimestamp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        // Global variables for Firebase instances
        let app;
        let db;
        let auth;
        let currentUserId = null;
        let currentUserEmail = null;
        let isHR = false;

        // Message Box Utility
        const messageBox = document.getElementById('messageBox');
        function showMessageBox(message, duration = 3000) {
            messageBox.textContent = message;
            messageBox.classList.add('show');
            setTimeout(() => {
                messageBox.classList.remove('show');
            }, duration);
        }

        // DOM Elements
        const loginView = document.getElementById('login-view');
        const employeeDashboardView = document.getElementById('employee-dashboard-view');
        const hrDashboardView = document.getElementById('hr-dashboard-view');

        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const employeeLoginBtn = document.getElementById('employee-login-btn');
        const hrLoginBtn = document.getElementById('hr-login-btn');
        const employeeLogoutBtn = document.getElementById('employee-logout-btn');
        const hrLogoutBtn = document.getElementById('hr-logout-btn');
        const markAttendanceBtn = document.getElementById('mark-attendance-btn');

        const employeeNameSpan = document.getElementById('employee-name');
        const employeeUserIdSpan = document.getElementById('employee-user-id');
        const hrUserIdSpan = document.getElementById('hr-user-id');
        const employeeAttendanceTableBody = document.querySelector('#employee-attendance-table tbody');
        const allAttendanceTableBody = document.querySelector('#all-attendance-table tbody');

        const statTotalEmployees = document.getElementById('stat-total-employees');
        const statTotalEntries = document.getElementById('stat-total-entries');
        const statTodayUnique = document.getElementById('stat-today-unique');

        // Predefined HR and Employee credentials for demonstration
        // In a real application, you would use Firebase Authentication for user management
        const HR_EMAIL = "<EMAIL>";
        const HR_PASSWORD = "password123";
        const EMPLOYEE_EMAIL = "<EMAIL>";
        const EMPLOYEE_PASSWORD = "password123";

        /**
         * Initializes Firebase and sets up authentication listener.
         */
        async function initializeFirebaseAndAuth() {
            try {
                // Retrieve Firebase configuration and app ID from global variables
                const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';
                const firebaseConfig = JSON.parse(typeof __firebase_config !== 'undefined' ? __firebase_config : '{}');

                // Initialize Firebase app
                app = initializeApp(firebaseConfig);
                db = getFirestore(app);
                auth = getAuth(app);

                // Sign in with custom token if available, otherwise anonymously
                if (typeof __initial_auth_token !== 'undefined' && __initial_auth_token) {
                    await signInWithCustomToken(auth, __initial_auth_token);
                    console.log("Signed in with custom token.");
                } else {
                    await signInAnonymously(auth);
                    console.log("Signed in anonymously.");
                }

                // Listen for authentication state changes
                onAuthStateChanged(auth, (user) => {
                    if (user) {
                        currentUserId = user.uid;
                        currentUserEmail = user.email;
                        console.log("Auth state changed. User ID:", currentUserId, "Email:", currentUserEmail);
                        // Determine if the user is HR or an employee
                        isHR = (currentUserEmail === HR_EMAIL);
                        updateUI(user);
                        setupRealtimeListeners(); // Setup listeners after user is authenticated
                    } else {
                        currentUserId = null;
                        currentUserEmail = null;
                        isHR = false;
                        updateUI(null);
                        console.log("User logged out or no user.");
                    }
                });

            } catch (error) {
                console.error("Error initializing Firebase or signing in:", error);
                showMessageBox("Failed to initialize app. Please try again.");
            }
        }

        /**
         * Updates the UI based on the current authentication state and user role.
         * @param {Object} user - The Firebase user object, or null if logged out.
         */
        function updateUI(user) {
            if (user) {
                loginView.classList.add('hidden');
                if (isHR) {
                    employeeDashboardView.classList.add('hidden');
                    hrDashboardView.classList.remove('hidden');
                    hrUserIdSpan.textContent = currentUserId;
                } else {
                    hrDashboardView.classList.add('hidden');
                    employeeDashboardView.classList.remove('hidden');
                    employeeNameSpan.textContent = currentUserEmail || 'Employee'; // Display email as name for simplicity
                    employeeUserIdSpan.textContent = currentUserId;
                }
            } else {
                loginView.classList.remove('hidden');
                employeeDashboardView.classList.add('hidden');
                hrDashboardView.classList.add('hidden');
                emailInput.value = '';
                passwordInput.value = '';
            }
        }

        /**
         * Handles user login.
         * @param {string} role - 'employee' or 'hr'.
         */
        async function handleLogin(role) {
            const email = emailInput.value;
            const password = passwordInput.value;

            let expectedEmail, expectedPassword;
            if (role === 'employee') {
                expectedEmail = EMPLOYEE_EMAIL;
                expectedPassword = EMPLOYEE_PASSWORD;
            } else { // HR
                expectedEmail = HR_EMAIL;
                expectedPassword = HR_PASSWORD;
            }

            if (email !== expectedEmail || password !== expectedPassword) {
                showMessageBox("Invalid credentials for " + role + " login.");
                return;
            }

            try {
                await signInWithEmailAndPassword(auth, email, password);
                showMessageBox("Logged in successfully as " + role + "!");
            } catch (error) {
                console.error("Login Error:", error);
                showMessageBox("Login failed: " + error.message);
            }
        }

        /**
         * Handles user logout.
         */
        async function handleLogout() {
            try {
                await signOut(auth);
                showMessageBox("Logged out successfully!");
            } catch (error) {
                console.error("Logout Error:", error);
                showMessageBox("Logout failed: " + error.message);
            }
        }

        /**
         * Marks attendance for the current employee.
         */
        async function markAttendance() {
            if (!currentUserId || isHR) {
                showMessageBox("Only employees can mark attendance.");
                return;
            }

            try {
                const now = new Date();
                const dateString = now.toISOString().split('T')[0]; // YYYY-MM-DD
                const timeString = now.toTimeString().split(' ')[0]; // HH:MM:SS

                const attendanceData = {
                    userId: currentUserId,
                    userName: currentUserEmail || 'Unknown Employee',
                    timestamp: serverTimestamp(), // Use server timestamp for consistency
                    date: dateString,
                    time: timeString,
                    type: 'Clock In' // For simplicity, only 'Clock In' is implemented
                };

                // Add to public collection for HR access
                const publicAttendanceRef = collection(db, `artifacts/${__app_id}/public/data/all_attendance`);
                await addDoc(publicAttendanceRef, attendanceData);

                showMessageBox("Attendance marked successfully!");
            } catch (error) {
                console.error("Error marking attendance:", error);
                showMessageBox("Failed to mark attendance: " + error.message);
            }
        }

        /**
         * Sets up real-time Firestore listeners based on user role.
         */
        function setupRealtimeListeners() {
            if (!db || !auth.currentUser) {
                console.warn("Firestore or user not ready for listeners.");
                return;
            }

            const appId = typeof __app_id !== 'undefined' ? __app_id : 'default-app-id';

            // Listener for all attendance records (for HR and potentially employee's own view)
            const allAttendanceRef = collection(db, `artifacts/${appId}/public/data/all_attendance`);
            onSnapshot(allAttendanceRef, (snapshot) => {
                const allAttendance = [];
                const employeeAttendance = [];
                const uniqueUsersToday = new Set();
                const today = new Date().toISOString().split('T')[0];

                snapshot.forEach(doc => {
                    const data = doc.data();
                    allAttendance.push(data);

                    if (data.userId === currentUserId) {
                        employeeAttendance.push(data);
                    }

                    if (data.date === today) {
                        uniqueUsersToday.add(data.userId);
                    }
                });

                // Sort attendance by timestamp descending
                allAttendance.sort((a, b) => b.timestamp?.toDate() - a.timestamp?.toDate());
                employeeAttendance.sort((a, b) => b.timestamp?.toDate() - a.timestamp?.toDate());

                // Update Employee Attendance Table
                if (!isHR) { // Only update if it's an employee
                    displayEmployeeAttendance(employeeAttendance);
                }

                // Update HR Dashboard
                if (isHR) {
                    displayAllAttendance(allAttendance);
                    updateHrStatistics(allAttendance, uniqueUsersToday.size);
                }
            }, (error) => {
                console.error("Error listening to all attendance:", error);
                showMessageBox("Error fetching attendance data.");
            });
        }

        /**
         * Displays employee's attendance history in the table.
         * @param {Array} attendanceRecords - Array of attendance objects.
         */
        function displayEmployeeAttendance(attendanceRecords) {
            employeeAttendanceTableBody.innerHTML = ''; // Clear existing rows
            if (attendanceRecords.length === 0) {
                employeeAttendanceTableBody.innerHTML = '<tr><td colspan="3" class="text-center text-gray-500 py-4">No attendance records found.</td></tr>';
                return;
            }
            attendanceRecords.forEach(record => {
                const row = employeeAttendanceTableBody.insertRow();
                row.innerHTML = `
                    <td>${record.date}</td>
                    <td>${record.time}</td>
                    <td>${record.type}</td>
                `;
            });
        }

        /**
         * Displays all attendance records for HR.
         * @param {Array} attendanceRecords - Array of all attendance objects.
         */
        function displayAllAttendance(attendanceRecords) {
            allAttendanceTableBody.innerHTML = ''; // Clear existing rows
            if (attendanceRecords.length === 0) {
                allAttendanceTableBody.innerHTML = '<tr><td colspan="5" class="text-center text-gray-500 py-4">No attendance records found.</td></tr>';
                return;
            }
            attendanceRecords.forEach(record => {
                const row = allAttendanceTableBody.insertRow();
                row.innerHTML = `
                    <td>${record.userId}</td>
                    <td>${record.userName}</td>
                    <td>${record.date}</td>
                    <td>${record.time}</td>
                    <td>${record.type}</td>
                `;
            });
        }

        /**
         * Updates HR statistics.
         * @param {Array} allAttendance - All attendance records.
         * @param {number} uniqueTodayCount - Number of unique users clocked in today.
         */
        function updateHrStatistics(allAttendance, uniqueTodayCount) {
            const uniqueEmployees = new Set(allAttendance.map(record => record.userId));
            statTotalEmployees.textContent = uniqueEmployees.size;
            statTotalEntries.textContent = allAttendance.length;
            statTodayUnique.textContent = uniqueTodayCount;
        }

        // Event Listeners
        employeeLoginBtn.addEventListener('click', () => handleLogin('employee'));
        hrLoginBtn.addEventListener('click', () => handleLogin('hr'));
        employeeLogoutBtn.addEventListener('click', handleLogout);
        hrLogoutBtn.addEventListener('click', handleLogout);
        markAttendanceBtn.addEventListener('click', markAttendance);

        // Initialize the application when the window loads
        window.onload = initializeFirebaseAndAuth;

    </script>
</body>
</html>
