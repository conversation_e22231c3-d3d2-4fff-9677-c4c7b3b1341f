<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excellence College - Courses, Fees & Admission Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
        }

        .header h1 {
            font-size: 2.8rem;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
        }

        /* Navigation */
        .nav {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }

        .nav li {
            margin: 0 1rem;
        }

        .nav a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav a:hover, .nav a.active {
            background: #667eea;
            color: white;
        }

        /* Dashboard Stats */
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 1.1rem;
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.7;
        }

        /* Section Styling */
        .section {
            background: white;
            margin-bottom: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .section-header h2 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .section-content {
            padding: 2rem;
        }

        /* Dashboard Charts */
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .chart-container {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .chart-title {
            text-align: center;
            color: #667eea;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        /* Admission Table */
        .admission-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .admission-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1rem;
            text-align: center;
            font-weight: 600;
        }

        .admission-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .admission-table tr:hover {
            background: #f8f9fa;
        }

        .admission-table .year-header {
            background: #e3f2fd;
            font-weight: bold;
            color: #1976d2;
        }

        /* Course Cards */
        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .course-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .course-card:hover {
            transform: translateY(-5px);
        }

        .course-header {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .course-header h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .course-duration {
            background: rgba(255,255,255,0.2);
            padding: 0.3rem 1rem;
            border-radius: 20px;
            display: inline-block;
            font-size: 0.9rem;
        }

        .course-details {
            padding: 1.5rem;
        }

        .course-details h4 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .course-details ul {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .course-details li {
            padding: 0.3rem 0;
            position: relative;
            padding-left: 1.5rem;
        }

        .course-details li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }

        /* Fee Structure Table */
        .fee-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .fee-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .fee-table td {
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }

        .fee-table tr:hover {
            background: #f8f9fa;
        }

        .fee-amount {
            font-weight: bold;
            color: #667eea;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .nav ul {
                flex-direction: column;
                align-items: center;
            }

            .nav li {
                margin: 0.5rem 0;
            }

            .dashboard-stats {
                grid-template-columns: 1fr;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }

            .courses-grid {
                grid-template-columns: 1fr;
            }

            .fee-table, .admission-table {
                font-size: 0.9rem;
            }

            .fee-table th, .fee-table td,
            .admission-table th, .admission-table td {
                padding: 0.5rem;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section, .stat-card, .chart-container {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 0.8rem 2rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2, #fcb69f);
            color: #333;
        }

        /* Filter Buttons */
        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn.active, .filter-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1>Excellence College of Technology</h1>
            <p>Comprehensive Academic Dashboard & Course Information</p>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <ul>
                <li><a href="#dashboard" class="active">Dashboard</a></li>
                <li><a href="#courses">Courses</a></li>
                <li><a href="#fees">Fee Structure</a></li>
                <li><a href="#admissions">Admissions Data</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <div class="container">
            <!-- Dashboard Overview -->
            <section id="dashboard" class="section">
                <div class="section-header">
                    <h2>Admission Dashboard (2022-2025)</h2>
                    <p>Real-time statistics and trends of student admissions</p>
                </div>
                <div class="section-content">
                    <!-- Dashboard Stats -->
                    <div class="dashboard-stats">
                        <div class="stat-card">
                            <div class="stat-icon">🎓</div>
                            <div class="stat-number" id="totalStudents">3,450</div>
                            <div class="stat-label">Total Students (2022-2025)</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📚</div>
                            <div class="stat-number">6</div>
                            <div class="stat-label">Active Courses</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📈</div>
                            <div class="stat-number" id="growthRate">15%</div>
                            <div class="stat-label">Growth Rate (2024-2025)</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🏆</div>
                            <div class="stat-number">95%</div>
                            <div class="stat-label">Placement Rate</div>
                        </div>
                    </div>

                    <!-- Charts Section -->
                    <div class="charts-grid">
                        <div class="chart-container">
                            <div class="chart-title">Year-wise Admission Trends</div>
                            <canvas id="admissionTrendChart"></canvas>
                        </div>
                        <div class="chart-container">
                            <div class="chart-title">Course-wise Distribution (2025)</div>
                            <canvas id="courseDistributionChart"></canvas>
                        </div>
                        <div class="chart-container">
                            <div class="chart-title">Gender Distribution</div>
                            <canvas id="genderChart"></canvas>
                        </div>
                        <div class="chart-container">
                            <div class="chart-title">Monthly Admission Pattern (2025)</div>
                            <canvas id="monthlyAdmissionChart"></canvas>
                        </div>
                    </div>
                </div>
            </section>

    <script>
        // Admission data for charts
        const admissionData = {
            years: ['2022', '2023', '2024', '2025'],
            totalAdmissions: [750, 820, 950, 930],
            courseWiseData: {
                'Computer Science': [180, 200, 230, 220],
                'Mechanical': [150, 160, 180, 175],
                'Electrical': [120, 130, 150, 145],
                'Civil': [100, 110, 130, 125],
                'BBA': [120, 140, 160, 155],
                'B.Com': [80, 80, 100, 110]
            },
            genderData: {
                male: [450, 480, 520, 510],
                female: [300, 340, 430, 420]
            }
        };

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            updateStats();
        });

        function initializeCharts() {
            // Admission Trend Chart
            const trendCtx = document.getElementById('admissionTrendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: admissionData.years,
                    datasets: [{
                        label: 'Total Admissions',
                        data: admissionData.totalAdmissions,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    }
                }
            });

            // Course Distribution Chart
            const courseCtx = document.getElementById('courseDistributionChart').getContext('2d');
            const courseData2025 = Object.keys(admissionData.courseWiseData).map(course => 
                admissionData.courseWiseData[course][3]
            );
            
            new Chart(courseCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(admissionData.courseWiseData),
                    datasets: [{
                        data: courseData2025,
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#4facfe',
                            '#00f2fe',
                            '#43e97b',
                            '#38f9d7'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Gender Distribution Chart
            const genderCtx = document.getElementById('genderChart').getContext('2d');
            new Chart(genderCtx, {
                type: 'bar',
                data: {
                    labels: admissionData.years,
                    datasets: [{
                        label: 'Male',
                        data: admissionData.genderData.male,
                        backgroundColor: '#667eea',
                        borderRadius: 5
                    }, {
                        label: 'Female',
                        data: admissionData.genderData.female,
                        backgroundColor: '#764ba2',
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        x: {
                            stacked: true
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true
                        }
                    }
                }
            });

            // Monthly Admission Pattern Chart
            const monthlyCtx = document.getElementById('monthlyAdmissionChart').getContext('2d');
            const monthlyData = [50, 80, 120, 150, 180, 200, 85, 45, 20, 0, 0, 0];
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            
            new Chart(monthlyCtx, {
                type: 'bar',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'Admissions',
                        data: monthlyData,
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: '#667eea',
                        borderWidth: 1,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateStats() {
            // Calculate total students
            const total = admissionData.totalAdmissions.reduce((sum, val) => sum + val, 0);
            document.getElementById('totalStudents').textContent = total.toLocaleString();
            
            // Calculate growth rate
            const current = admissionData.totalAdmissions[3];
            const previous = admissionData.totalAdmissions[2];
            const growthRate = ((current - previous) / previous * 100).toFixed(1);
            document.getElementById('growthRate').textContent = growthRate + '%';
        }

        // Smooth scrolling for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Update active navigation
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav a');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (scrollY >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>

            <!-- Courses Section -->
            <section id="courses" class="section">
                <div class="section-header">
                    <h2>Our Courses</h2>
                    <p>Comprehensive programs designed for future leaders</p>
                </div>
                <div class="section-content">
                    <div class="courses-grid">
                        <!-- Computer Science Engineering -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Computer Science Engineering</h3>
                                <span class="course-duration">4 Years (B.Tech)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Programming Languages (C, C++, Java, Python)</li>
                                    <li>Data Structures & Algorithms</li>
                                    <li>Database Management Systems</li>
                                    <li>Web Development & Mobile Apps</li>
                                    <li>Machine Learning & AI</li>
                                    <li>Software Engineering</li>
                                    <li>Cybersecurity Fundamentals</li>
                                    <li>Industry Internships</li>
                                </ul>
                                <h4>Admission Stats (2022-2025)</h4>
                                <ul>
                                    <li>2022: 180 students</li>
                                    <li>2023: 200 students</li>
                                    <li>2024: 230 students</li>
                                    <li>2025: 220 students</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Mechanical Engineering -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Mechanical Engineering</h3>
                                <span class="course-duration">4 Years (B.Tech)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Engineering Mechanics</li>
                                    <li>Thermodynamics</li>
                                    <li>Machine Design</li>
                                    <li>Manufacturing Processes</li>
                                    <li>CAD/CAM</li>
                                    <li>Robotics & Automation</li>
                                    <li>Quality Control</li>
                                    <li>Industrial Training</li>
                                </ul>
                                <h4>Admission Stats (2022-2025)</h4>
                                <ul>
                                    <li>2022: 150 students</li>
                                    <li>2023: 160 students</li>
                                    <li>2024: 180 students</li>
                                    <li>2025: 175 students</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Electrical Engineering -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Electrical Engineering</h3>
                                <span class="course-duration">4 Years (B.Tech)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Circuit Analysis</li>
                                    <li>Power Systems</li>
                                    <li>Control Systems</li>
                                    <li>Electronics & Communication</li>
                                    <li>Renewable Energy</li>
                                    <li>Electric Machines</li>
                                    <li>Power Electronics</li>
                                    <li>Project Work</li>
                                </ul>
                                <h4>Admission Stats (2022-2025)</h4>
                                <ul>
                                    <li>2022: 120 students</li>
                                    <li>2023: 130 students</li>
                                    <li>2024: 150 students</li>
                                    <li>2025: 145 students</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Civil Engineering -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Civil Engineering</h3>
                                <span class="course-duration">4 Years (B.Tech)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Structural Engineering</li>
                                    <li>Environmental Engineering</li>
                                    <li>Transportation Engineering</li>
                                    <li>Geotechnical Engineering</li>
                                    <li>Construction Management</li>
                                    <li>Building Information Modeling</li>
                                    <li>Surveying & GIS</li>
                                    <li>Site Visits & Practical Training</li>
                                </ul>
                                <h4>Admission Stats (2022-2025)</h4>
                                <ul>
                                    <li>2022: 100 students</li>
                                    <li>2023: 110 students</li>
                                    <li>2024: 130 students</li>
                                    <li>2025: 125 students</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Business Administration -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Business Administration</h3>
                                <span class="course-duration">3 Years (BBA)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Business Management</li>
                                    <li>Marketing & Sales</li>
                                    <li>Financial Management</li>
                                    <li>Human Resource Management</li>
                                    <li>Operations Management</li>
                                    <li>Business Analytics</li>
                                    <li>Entrepreneurship</li>
                                    <li>Industry Exposure</li>
                                </ul>
                                <h4>Admission Stats (2022-2025)</h4>
                                <ul>
                                    <li>2022: 120 students</li>
                                    <li>2023: 140 students</li>
                                    <li>2024: 160 students</li>
                                    <li>2025: 155 students</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Commerce -->
                        <div class="course-card">
                            <div class="course-header">
                                <h3>Commerce</h3>
                                <span class="course-duration">3 Years (B.Com)</span>
                            </div>
                            <div class="course-details">
                                <h4>Course Highlights</h4>
                                <ul>
                                    <li>Accounting & Auditing</li>
                                    <li>Banking & Finance</li>
                                    <li>Taxation</li>
                                    <li>Business Law</li>
                                    <li>Economics</li>
                                    <li>Computer Applications</li>
                                    <li>Business Communication</li>
                                    <li>Practical Training</li>
                                </ul>
                                <h4>Admission Stats (2022-2025)</h4>
                                <ul>
                                    <li>2022: 80 students</li>
                                    <li>2023: 80 students</li>
                                    <li>2024: 100 students</li>
                                    <li>2025: 110 students</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Fee Structure Section -->
            <section id="fees" class="section">
                <div class="section-header">
                    <h2>Fee Structure (3 Years)</h2>
                    <p>Transparent and affordable fee structure for all courses</p>
                </div>
                <div class="section-content">
                    <!-- Engineering Courses Fee Structure -->
                    <h3 style="color: #667eea; margin-bottom: 1rem; font-size: 1.5rem;">Engineering Courses (B.Tech - 4 Years)</h3>
                    <table class="fee-table">
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>Year 1</th>
                                <th>Year 2</th>
                                <th>Year 3</th>
                                <th>Year 4</th>
                                <th>Total (4 Years)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Computer Science Engineering</strong></td>
                                <td class="fee-amount">₹1,50,000</td>
                                <td class="fee-amount">₹1,55,000</td>
                                <td class="fee-amount">₹1,60,000</td>
                                <td class="fee-amount">₹1,65,000</td>
                                <td class="fee-amount"><strong>₹6,30,000</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Mechanical Engineering</strong></td>
                                <td class="fee-amount">₹1,40,000</td>
                                <td class="fee-amount">₹1,45,000</td>
                                <td class="fee-amount">₹1,50,000</td>
                                <td class="fee-amount">₹1,55,000</td>
                                <td class="fee-amount"><strong>₹5,90,000</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Electrical Engineering</strong></td>
                                <td class="fee-amount">₹1,35,000</td>
                                <td class="fee-amount">₹1,40,000</td>
                                <td class="fee-amount">₹1,45,000</td>
                                <td class="fee-amount">₹1,50,000</td>
                                <td class="fee-amount"><strong>₹5,70,000</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Civil Engineering</strong></td>
                                <td class="fee-amount">₹1,30,000</td>
                                <td class="fee-amount">₹1,35,000</td>
                                <td class="fee-amount">₹1,40,000</td>
                                <td class="fee-amount">₹1,45,000</td>
                                <td class="fee-amount"><strong>₹5,50,000</strong></td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Management & Commerce Courses Fee Structure -->
                    <h3 style="color: #667eea; margin: 2rem 0 1rem 0; font-size: 1.5rem;">Management & Commerce Courses (3 Years)</h3>
                    <table class="fee-table">
                        <thead>
                            <tr>
                                <th>Course</th>
                                <th>Year 1</th>
                                <th>Year 2</th>
                                <th>Year 3</th>
                                <th>Total (3 Years)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Business Administration (BBA)</strong></td>
                                <td class="fee-amount">₹80,000</td>
                                <td class="fee-amount">₹85,000</td>
                                <td class="fee-amount">₹90,000</td>
                                <td class="fee-amount"><strong>₹2,55,000</strong></td>
                            </tr>
                            <tr>
                                <td><strong>Commerce (B.Com)</strong></td>
                                <td class="fee-amount">₹60,000</td>
                                <td class="fee-amount">₹65,000</td>
                                <td class="fee-amount">₹70,000</td>
                                <td class="fee-amount"><strong>₹1,95,000</strong></td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Fee Components Breakdown -->
                    <div style="margin-top: 2rem; background: #f8f9fa; padding: 1.5rem; border-radius: 10px;">
                        <h4 style="color: #667eea; margin-bottom: 1rem;">Fee Components Include:</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                            <div>
                                <h5 style="color: #333; margin-bottom: 0.5rem;">Academic Fees</h5>
                                <ul style="list-style: none; padding-left: 0;">
                                    <li>✓ Tuition Fees</li>
                                    <li>✓ Laboratory Fees</li>
                                    <li>✓ Library Fees</li>
                                    <li>✓ Examination Fees</li>
                                </ul>
                            </div>
                            <div>
                                <h5 style="color: #333; margin-bottom: 0.5rem;">Infrastructure Fees</h5>
                                <ul style="list-style: none; padding-left: 0;">
                                    <li>✓ Development Fees</li>
                                    <li>✓ Maintenance Fees</li>
                                    <li>✓ Sports & Recreation</li>
                                    <li>✓ Technology Fees</li>
                                </ul>
                            </div>
                            <div>
                                <h5 style="color: #333; margin-bottom: 0.5rem;">Additional Services</h5>
                                <ul style="list-style: none; padding-left: 0;">
                                    <li>✓ Student Activities</li>
                                    <li>✓ Placement Support</li>
                                    <li>✓ Industry Training</li>
                                    <li>✓ Career Counseling</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
</body>
</html>
