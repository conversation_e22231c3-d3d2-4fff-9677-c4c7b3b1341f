/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e3a8a;
}

.nav-logo i {
    margin-right: 10px;
    font-size: 2rem;
    color: #3b82f6;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
}

.nav-menu li {
    margin-left: 30px;
    position: relative;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
}

.nav-menu a:hover {
    color: #3b82f6;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 10px 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    padding: 10px 20px;
    display: block;
    border-radius: 0;
}

.dropdown-menu a:hover {
    background: #f8fafc;
}

.apply-btn {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    color: white !important;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #06b6d4 100%);
    z-index: -2;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('https://images.unsplash.com/photo-1562774053-701939374585?w=1920&h=1080&fit=crop') center/cover;
    opacity: 0.2;
    z-index: -1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(30, 58, 138, 0.7);
    z-index: -1;
}

.hero-content {
    max-width: 800px;
    margin-bottom: 60px;
    animation: fadeInUp 1s ease-out;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(245, 158, 11, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #1e3a8a;
    transform: translateY(-3px);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 40px;
    max-width: 600px;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.stat {
    text-align: center;
}

.stat h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #fbbf24;
}

.stat p {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* About Section */
.about {
    padding: 100px 0;
    background: #f8fafc;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 30px;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 20px;
    color: #64748b;
}

.about-features {
    display: grid;
    gap: 25px;
    margin-top: 40px;
}

.feature {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.feature i {
    font-size: 1.5rem;
    color: #3b82f6;
    margin-top: 5px;
}

.feature h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 8px;
}

.feature p {
    color: #64748b;
    line-height: 1.6;
}

.about-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Programs Section */
.programs {
    padding: 100px 0;
    background: white;
}

.programs h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 60px;
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.program-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.program-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.program-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
}

.program-icon i {
    font-size: 2rem;
    color: white;
}

.program-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.program-card p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 20px;
}

.program-card ul {
    list-style: none;
    margin-bottom: 25px;
}

.program-card ul li {
    color: #64748b;
    margin-bottom: 8px;
    position: relative;
    padding-left: 20px;
}

.program-card ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.learn-more {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.learn-more:hover {
    color: #1e40af;
}

/* Admissions Section */
.admissions {
    padding: 100px 0;
    background: #f8fafc;
}

.admissions h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 60px;
}

.admissions-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.admission-info h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 20px;
}

.admission-info p {
    color: #64748b;
    line-height: 1.8;
    margin-bottom: 30px;
}

.admission-requirements h4,
.important-dates h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 20px;
}

.admission-requirements ul {
    list-style: none;
    margin-bottom: 40px;
}

.admission-requirements li {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: #64748b;
}

.admission-requirements i {
    color: #10b981;
    margin-right: 12px;
}

.date-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e2e8f0;
}

.date-item:last-child {
    border-bottom: none;
}

.date {
    font-weight: 600;
    color: #1e3a8a;
}

.event {
    color: #64748b;
}

.application-form {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.application-form h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 30px;
    text-align: center;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Campus Life Section */
.campus-life {
    padding: 100px 0;
    background: white;
}

.campus-life h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 60px;
}

.campus-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 40px;
}

.campus-feature {
    display: flex;
    gap: 30px;
    align-items: center;
    background: #f8fafc;
    padding: 30px;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.campus-feature:hover {
    transform: translateY(-5px);
}

.campus-feature img {
    width: 200px;
    height: 150px;
    object-fit: cover;
    border-radius: 10px;
    flex-shrink: 0;
}

.campus-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 15px;
}

.campus-content p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 15px;
}

.campus-content ul {
    list-style: none;
}

.campus-content ul li {
    color: #64748b;
    margin-bottom: 8px;
    position: relative;
    padding-left: 20px;
}

.campus-content ul li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #3b82f6;
    font-weight: bold;
}

/* Faculty Section */
.faculty {
    padding: 100px 0;
    background: #f8fafc;
}

.faculty h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 20px;
}

.faculty-intro {
    text-align: center;
    font-size: 1.2rem;
    color: #64748b;
    margin-bottom: 60px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.faculty-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.faculty-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.faculty-card:hover {
    transform: translateY(-10px);
}

.faculty-card img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
    border: 4px solid #e2e8f0;
}

.faculty-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 8px;
}

.title {
    color: #3b82f6;
    font-weight: 500;
    margin-bottom: 5px;
}

.department {
    color: #64748b;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.credentials {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.credentials span {
    background: #e0f2fe;
    color: #0369a1;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.faculty-contact {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.faculty-contact a {
    width: 40px;
    height: 40px;
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: background 0.3s ease;
}

.faculty-contact a:hover {
    background: #1e40af;
}

/* News & Events Section */
.news-events {
    padding: 100px 0;
    background: white;
}

.news-events h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 60px;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.news-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.news-card:hover {
    transform: translateY(-10px);
}

.news-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.news-content {
    padding: 25px;
}

.news-date {
    color: #3b82f6;
    font-size: 0.9rem;
    font-weight: 500;
}

.news-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1e3a8a;
    margin: 10px 0 15px;
    line-height: 1.4;
}

.news-content p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 20px;
}

.read-more {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: #1e40af;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: #f8fafc;
}

.contact h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 60px;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 30px;
}

.contact-item i {
    font-size: 1.5rem;
    color: #3b82f6;
    margin-top: 5px;
}

.contact-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 8px;
}

.contact-item p {
    color: #64748b;
    line-height: 1.6;
}

.contact-form {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 30px;
    text-align: center;
}

/* Footer */
.footer {
    background: #1e3a8a;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 20px;
    font-weight: 600;
}

.footer-section p {
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 20px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: background 0.3s ease;
}

.social-links a:hover {
    background: #3b82f6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0.8;
}

.footer-bottom a {
    color: #3b82f6;
    text-decoration: none;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .about-content,
    .admissions-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .campus-grid {
        grid-template-columns: 1fr;
    }

    .campus-feature {
        flex-direction: column;
        text-align: center;
    }

    .campus-feature img {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 20px 0;
        height: calc(100vh - 80px);
        overflow-y: auto;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 15px 0;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background: #f8fafc;
        margin-top: 10px;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .programs-grid,
    .faculty-grid,
    .news-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-stats {
        grid-template-columns: 1fr;
    }

    .about-text h2,
    .programs h2,
    .admissions h2,
    .campus-life h2,
    .faculty h2,
    .news-events h2,
    .contact h2 {
        font-size: 2rem;
    }

    .program-card,
    .application-form,
    .contact-form {
        padding: 25px 20px;
    }

    .campus-feature {
        padding: 20px;
    }
}
