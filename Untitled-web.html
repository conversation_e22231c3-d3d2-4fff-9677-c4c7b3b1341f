<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Attendance System</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
        }
        .container {
            margin-top: 50px;
        }
        h1 {
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center">Digital Attendance System</h1>
        <form id="attendanceForm">
            <div class="form-group">
                <label for="name">Name</label>
                <input type="text" class="form-control" id="name" required>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" class="form-control" id="email" required>
            </div>
            <div class="form-group">
                <label for="date">Date</label>
                <input type="date" class="form-control" id="date" required>
            </div>
            <button type="submit" class="btn btn-primary">Submit Attendance</button>
        </form>
        <hr>
        <h2 class="mt-5">Attendance Records</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody id="attendanceRecords">
                <!-- Attendance records will be inserted here -->
            </tbody>
        </table>
    </div>
    <script>
        document.getElementById('attendanceForm').addEventListener('submit', function(event) {
            event.preventDefault();
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const date = document.getElementById('date').value;
            const recordsTable = document.getElementById('attendanceRecords');
            const newRow = recordsTable.insertRow();
            const nameCell = newRow.insertCell(0);
            const emailCell = newRow.insertCell(1);
            const dateCell = newRow.insertCell(2);
            nameCell.textContent = name;
            emailCell.textContent = email;
            dateCell.textContent = date;
            this.reset(); // Reset the form
        });
    </script>
</body>
</html>