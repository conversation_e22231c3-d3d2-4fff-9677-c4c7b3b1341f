/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    text-decoration: none;
}

.logo i {
    margin-right: 10px;
    font-size: 2rem;
    color: #3498db;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    align-items: center;
}

.nav-menu li {
    margin-left: 30px;
}

.nav-menu a {
    text-decoration: none;
    color: #555;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #3498db;
}

.careers-link {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white !important;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.careers-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #555;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

.hero-content {
    max-width: 800px;
    margin-bottom: 60px;
    z-index: 1;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.highlight {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-content p {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #667eea;
    transform: translateY(-3px);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 40px;
    max-width: 600px;
    z-index: 1;
}

.stat-item {
    text-align: center;
}

.stat-item h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #f093fb;
}

.stat-item p {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* About Section */
.about {
    padding: 100px 0;
    background: #f8f9fa;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 30px;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 20px;
    color: #555;
}

.about-features {
    display: grid;
    gap: 25px;
    margin-top: 40px;
}

.feature {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.feature i {
    font-size: 1.5rem;
    color: #3498db;
    margin-top: 5px;
}

.feature h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.feature p {
    color: #555;
    line-height: 1.6;
}

.about-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Services Section */
.services {
    padding: 100px 0;
    background: white;
}

.services h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 60px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.service-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
}

.service-icon i {
    font-size: 2rem;
    color: white;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
}

.service-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.service-card ul {
    list-style: none;
    text-align: left;
    margin-bottom: 25px;
}

.service-card ul li {
    color: #666;
    margin-bottom: 8px;
    position: relative;
    padding-left: 20px;
}

.service-card ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
}

.service-link {
    color: #3498db;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.service-link:hover {
    color: #2980b9;
}

/* Portfolio Section */
.portfolio {
    padding: 100px 0;
    background: #f8f9fa;
}

.portfolio h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    color: #666;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn.active,
.filter-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.portfolio-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-5px);
}

.portfolio-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.9), rgba(41, 128, 185, 0.9));
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 30px;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-overlay h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.portfolio-overlay p {
    margin-bottom: 20px;
    opacity: 0.9;
}

.portfolio-links {
    display: flex;
    gap: 15px;
}

.portfolio-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: background 0.3s ease;
}

.portfolio-link:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Team Section */
.team {
    padding: 100px 0;
    background: white;
}

.team h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.team-member {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
}

.team-member img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
    border: 4px solid #e9ecef;
}

.member-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.role {
    color: #3498db;
    font-weight: 500;
    margin-bottom: 15px;
}

.bio {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #3498db;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: background 0.3s ease;
}

.social-links a:hover {
    background: #2980b9;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: #f8f9fa;
}

.contact h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 30px;
}

.contact-item i {
    font-size: 1.5rem;
    color: #3498db;
    margin-top: 5px;
}

.contact-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.contact-item p {
    color: #666;
    line-height: 1.6;
}

.contact-form {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-form h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 30px;
    text-align: center;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.footer-logo i {
    margin-right: 10px;
    font-size: 2rem;
    color: #3498db;
}

.footer-section h4 {
    margin-bottom: 20px;
    font-weight: 600;
    color: #ecf0f1;
}

.footer-section p {
    line-height: 1.6;
    opacity: 0.8;
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.contact-details p {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    opacity: 0.8;
}

.contact-details i {
    margin-right: 10px;
    width: 20px;
    color: #3498db;
}

.newsletter-signup {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.newsletter-signup input {
    flex: 1;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.newsletter-signup input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-signup button {
    padding: 10px 20px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.newsletter-signup button:hover {
    background: #2980b9;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

/* Careers Page Styles */
.careers-hero {
    min-height: 80vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
}

.careers-hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.careers-hero-content p {
    font-size: 1.2rem;
    margin-bottom: 50px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Why Work With Us */
.why-work-with-us {
    padding: 100px 0;
    background: #f8f9fa;
}

.why-work-with-us h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.benefit-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.benefit-card:hover {
    transform: translateY(-5px);
}

.benefit-card i {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 20px;
}

.benefit-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
}

.benefit-card p {
    color: #666;
    line-height: 1.6;
}

/* Job Listings */
.job-listings {
    padding: 100px 0;
    background: white;
}

.jobs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 50px;
    flex-wrap: wrap;
    gap: 20px;
}

.jobs-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
}

.job-filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.job-filters select {
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    color: #555;
    font-weight: 500;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.job-filters select:focus {
    outline: none;
    border-color: #3498db;
}

.jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
}

.job-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.job-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.job-header h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2c3e50;
    flex: 1;
}

.job-type {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.job-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

.job-meta span {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 0.9rem;
}

.job-meta i {
    margin-right: 5px;
    color: #3498db;
}

.job-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.job-requirements {
    margin-bottom: 20px;
}

.job-requirements h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.job-requirements ul {
    list-style: none;
    padding-left: 0;
}

.job-requirements li {
    color: #666;
    margin-bottom: 5px;
    position: relative;
    padding-left: 20px;
}

.job-requirements li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #3498db;
    font-weight: bold;
}

.job-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 25px;
}

.skill-tag {
    background: #f8f9fa;
    color: #495057;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.apply-btn {
    width: 100%;
    padding: 12px 20px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
}

/* Application Process */
.application-process {
    padding: 100px 0;
    background: #f8f9fa;
}

.application-process h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
}

.step {
    text-align: center;
    padding: 30px 20px;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 20px;
}

.step h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
}

.step p {
    color: #666;
    line-height: 1.6;
}

/* Testimonials */
.testimonials {
    padding: 100px 0;
    background: white;
}

.testimonials h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.testimonial-card {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    border-left: 4px solid #3498db;
}

.testimonial-content {
    margin-bottom: 25px;
}

.testimonial-content p {
    color: #666;
    line-height: 1.6;
    font-style: italic;
    font-size: 1.1rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.testimonial-author img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-author h4 {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.testimonial-author p {
    color: #3498db;
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 1;
    visibility: visible;
    transition: all 0.3s ease;
}

.modal.hidden {
    opacity: 0;
    visibility: hidden;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transform: scale(1);
    transition: transform 0.3s ease;
}

.modal.hidden .modal-content {
    transform: scale(0.9);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: #f8f9fa;
}

.modal-body {
    padding: 30px;
}

.application-form {
    max-width: none;
}

.form-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}

.form-section:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.file-upload {
    position: relative;
    display: inline-block;
    width: 100%;
}

.file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-upload-label:hover {
    border-color: #3498db;
    background: #e3f2fd;
}

.file-upload-label i {
    font-size: 2rem;
    color: #3498db;
    margin-bottom: 10px;
}

.file-upload-label span {
    color: #666;
    font-weight: 500;
}

.file-upload-label small {
    color: #999;
    font-size: 0.8rem;
    margin-top: 5px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-30 {
    margin-bottom: 30px;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .jobs-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .job-filters {
        width: 100%;
        justify-content: flex-start;
    }

    .jobs-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 20px 0;
        height: calc(100vh - 80px);
        overflow-y: auto;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu ul {
        flex-direction: column;
        width: 100%;
    }

    .nav-menu li {
        margin: 15px 0;
    }

    .hero-content h1,
    .careers-hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-content p {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .services-grid,
    .portfolio-grid,
    .team-grid,
    .benefits-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .process-steps {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-header,
    .modal-body {
        padding: 20px;
    }

    .job-filters {
        flex-direction: column;
    }

    .job-filters select {
        width: 100%;
    }

    .newsletter-signup {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-stats {
        grid-template-columns: 1fr;
    }

    .about-text h2,
    .services h2,
    .portfolio h2,
    .team h2,
    .contact h2,
    .why-work-with-us h2,
    .application-process h2,
    .testimonials h2 {
        font-size: 2rem;
    }

    .service-card,
    .contact-form,
    .benefit-card,
    .job-card {
        padding: 25px 20px;
    }

    .jobs-grid {
        grid-template-columns: 1fr;
    }

    .job-card {
        min-width: auto;
    }

    .job-meta {
        flex-direction: column;
        gap: 8px;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    .modal,
    .btn {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    .hero,
    .careers-hero {
        background: none !important;
        color: black !important;
        min-height: auto;
        padding: 20px 0;
    }

    .section-subtitle {
        page-break-after: avoid;
    }

    .job-card,
    .service-card,
    .benefit-card {
        page-break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ccc;
    }
}
