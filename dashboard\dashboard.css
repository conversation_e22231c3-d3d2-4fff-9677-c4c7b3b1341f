/* Dashboard Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f6fa;
    color: #333;
    overflow-x: hidden;
}

/* Sidebar */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 260px;
    height: 100vh;
    background: #2c3e50;
    color: white;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #34495e;
    display: flex;
    align-items: center;
}

.sidebar-header i {
    font-size: 1.5rem;
    margin-right: 10px;
    color: #3498db;
}

.sidebar-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover,
.nav-item.active {
    background: #34495e;
    color: white;
    border-left-color: #3498db;
}

.nav-item i {
    width: 20px;
    margin-right: 15px;
    font-size: 1.1rem;
}

.sidebar-footer {
    position: absolute;
    bottom: 20px;
    width: 100%;
    padding: 0 20px;
}

.back-to-website {
    display: flex;
    align-items: center;
    padding: 10px;
    color: #bdc3c7;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.back-to-website:hover {
    background: #34495e;
    color: white;
}

.back-to-website i {
    margin-right: 10px;
}

/* Main Content */
.main-content {
    margin-left: 260px;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

/* Header */
.header {
    background: white;
    padding: 0 30px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    margin-right: 20px;
    cursor: pointer;
    color: #666;
    display: none;
}

.header h1 {
    font-size: 1.5rem;
    color: #2c3e50;
    font-weight: 600;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 15px;
    color: #666;
}

.search-box input {
    padding: 10px 15px 10px 40px;
    border: 1px solid #ddd;
    border-radius: 25px;
    width: 300px;
    font-size: 0.9rem;
}

.notifications {
    position: relative;
    cursor: pointer;
    color: #666;
    font-size: 1.2rem;
}

.notification-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-profile {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 25px;
    transition: background 0.3s ease;
}

.user-profile:hover {
    background: #f8f9fa;
}

.user-profile img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    margin-right: 10px;
}

.user-profile span {
    margin-right: 10px;
    font-weight: 500;
}

/* Dashboard Content */
.dashboard-content {
    padding: 30px;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.patients { background: #3498db; }
.stat-icon.appointments { background: #2ecc71; }
.stat-icon.doctors { background: #9b59b6; }
.stat-icon.revenue { background: #f39c12; }

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: #2c3e50;
}

.stat-info p {
    color: #666;
    margin-bottom: 5px;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive { color: #27ae60; }
.stat-change.negative { color: #e74c3c; }
.stat-change.neutral { color: #95a5a6; }

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container,
.recent-activity {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.chart-container h3,
.recent-activity h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.2rem;
}

.chart-container canvas {
    max-height: 300px;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 0;
    border-bottom: 1px solid #ecf0f1;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ecf0f1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #666;
}

.activity-info p {
    margin-bottom: 5px;
    color: #2c3e50;
}

.activity-info span {
    font-size: 0.8rem;
    color: #95a5a6;
}

/* Quick Actions */
.quick-actions {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.quick-actions h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.2rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.action-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.action-btn i {
    margin-right: 10px;
    font-size: 1.1rem;
}

/* Patients Table */
.patients-table {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.patients-table h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.2rem;
}

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th,
table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status.admitted {
    background: #d4edda;
    color: #155724;
}

.status.outpatient {
    background: #d1ecf1;
    color: #0c5460;
}

.status.discharged {
    background: #f8d7da;
    color: #721c24;
}

.btn-small {
    padding: 5px 10px;
    margin: 0 2px;
    border: none;
    border-radius: 4px;
    background: #3498db;
    color: white;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background 0.3s ease;
}

.btn-small:hover {
    background: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar-toggle {
        display: block !important;
    }
    
    .header {
        padding: 0 15px;
    }
    
    .search-box input {
        width: 200px;
    }
    
    .dashboard-content {
        padding: 15px;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
}
