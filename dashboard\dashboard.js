// Dashboard JavaScript Functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard
    initializeDashboard();
    initializeChart();
    setupEventListeners();
});

function initializeDashboard() {
    // Set current date and time
    updateDateTime();
    setInterval(updateDateTime, 60000); // Update every minute
    
    // Initialize sidebar navigation
    initializeSidebarNavigation();
    
    // Load dashboard data
    loadDashboardData();
}

function updateDateTime() {
    const now = new Date();
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    // You can add a date/time display element if needed
    console.log('Current time:', now.toLocaleDateString('en-US', options));
}

function initializeSidebarNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    const contentSections = document.querySelectorAll('.content-section');
    
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all nav items
            navItems.forEach(nav => nav.classList.remove('active'));
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Hide all content sections
            contentSections.forEach(section => section.classList.remove('active'));
            
            // Show corresponding content section
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);
            if (targetSection) {
                targetSection.classList.add('active');
            }
            
            // Load section-specific data
            loadSectionData(targetId);
        });
    });
}

function loadSectionData(sectionId) {
    switch(sectionId) {
        case 'patients':
            loadPatientsData();
            break;
        case 'appointments':
            loadAppointmentsData();
            break;
        case 'doctors':
            loadDoctorsData();
            break;
        case 'departments':
            loadDepartmentsData();
            break;
        case 'inventory':
            loadInventoryData();
            break;
        case 'reports':
            loadReportsData();
            break;
        default:
            console.log('Loading overview data...');
    }
}

function loadDashboardData() {
    // Simulate loading dashboard statistics
    animateCounters();
    updateRecentActivity();
}

function animateCounters() {
    const counters = document.querySelectorAll('.stat-info h3');
    
    counters.forEach(counter => {
        const target = parseInt(counter.textContent.replace(/[^0-9]/g, ''));
        const increment = target / 100;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Format the number based on original format
            const originalText = counter.textContent;
            if (originalText.includes('$')) {
                counter.textContent = '$' + Math.floor(current).toLocaleString();
            } else {
                counter.textContent = Math.floor(current).toLocaleString();
            }
        }, 20);
    });
}

function updateRecentActivity() {
    // Simulate real-time activity updates
    const activities = [
        {
            icon: 'fas fa-user-plus',
            text: 'New patient registered',
            detail: 'Jane Doe - Just now',
            time: new Date()
        },
        {
            icon: 'fas fa-calendar',
            text: 'Appointment scheduled',
            detail: 'Dr. Wilson - 5 minutes ago',
            time: new Date(Date.now() - 5 * 60000)
        },
        {
            icon: 'fas fa-pills',
            text: 'Medication dispensed',
            detail: 'Pharmacy - 15 minutes ago',
            time: new Date(Date.now() - 15 * 60000)
        }
    ];
    
    // You can implement real-time updates here
    console.log('Recent activities updated');
}

function initializeChart() {
    const ctx = document.getElementById('patientChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Patient Visits',
                data: [1200, 1350, 1100, 1400, 1250, 1500],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

function setupEventListeners() {
    // Sidebar toggle for mobile
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
    }
    
    // Search functionality
    const searchInput = document.querySelector('.search-box input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            performSearch(searchTerm);
        });
    }
    
    // Quick action buttons
    const actionButtons = document.querySelectorAll('.action-btn');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const action = this.querySelector('span').textContent;
            handleQuickAction(action);
        });
    });
    
    // Notification bell
    const notificationBell = document.querySelector('.notifications');
    if (notificationBell) {
        notificationBell.addEventListener('click', function() {
            showNotifications();
        });
    }
    
    // User profile dropdown
    const userProfile = document.querySelector('.user-profile');
    if (userProfile) {
        userProfile.addEventListener('click', function() {
            showUserMenu();
        });
    }
}

function performSearch(searchTerm) {
    console.log('Searching for:', searchTerm);
    // Implement search functionality
    // This could search through patients, doctors, appointments, etc.
}

function handleQuickAction(action) {
    console.log('Quick action:', action);
    
    switch(action) {
        case 'Add Patient':
            showAddPatientModal();
            break;
        case 'Schedule Appointment':
            showScheduleAppointmentModal();
            break;
        case 'Prescribe Medication':
            showPrescriptionModal();
            break;
        case 'Generate Report':
            generateReport();
            break;
        default:
            console.log('Unknown action:', action);
    }
}

function showAddPatientModal() {
    alert('Add Patient modal would open here');
    // Implement modal for adding new patient
}

function showScheduleAppointmentModal() {
    alert('Schedule Appointment modal would open here');
    // Implement modal for scheduling appointments
}

function showPrescriptionModal() {
    alert('Prescription modal would open here');
    // Implement modal for prescribing medication
}

function generateReport() {
    alert('Report generation would start here');
    // Implement report generation functionality
}

function showNotifications() {
    alert('Notifications panel would open here');
    // Implement notifications panel
}

function showUserMenu() {
    alert('User menu would open here');
    // Implement user profile menu
}

// Section-specific data loading functions
function loadPatientsData() {
    console.log('Loading patients data...');
    // Implement patients data loading
}

function loadAppointmentsData() {
    console.log('Loading appointments data...');
    // Implement appointments data loading
}

function loadDoctorsData() {
    console.log('Loading doctors data...');
    // Implement doctors data loading
}

function loadDepartmentsData() {
    console.log('Loading departments data...');
    // Implement departments data loading
}

function loadInventoryData() {
    console.log('Loading inventory data...');
    // Implement inventory data loading
}

function loadReportsData() {
    console.log('Loading reports data...');
    // Implement reports data loading
}

// Utility functions
function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }).format(date);
}

function formatTime(date) {
    return new Intl.DateTimeFormat('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}

function showToast(message, type = 'info') {
    // Implement toast notification system
    console.log(`Toast (${type}): ${message}`);
}

// Export functions for external use
window.dashboardAPI = {
    loadSectionData,
    showToast,
    formatDate,
    formatTime
};
