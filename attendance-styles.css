/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f5f6fa;
}

.hidden {
    display: none !important;
}

/* Login Page Styles */
.login-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.logo i {
    font-size: 3rem;
    margin-right: 15px;
    color: #ffd700;
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

.login-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Login Type Selector */
.login-type-selector {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 5px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.login-type-btn {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    background: transparent;
    border: none;
    color: white;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 500;
}

.login-type-btn.active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.login-type-btn i {
    margin-right: 10px;
    font-size: 1.2rem;
}

/* Login Form Container */
.login-form-container {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 450px;
    margin-bottom: 30px;
}

.login-form h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 1.8rem;
    font-weight: 600;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    left: 15px;
    color: #666;
    z-index: 2;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #667eea;
}

.password-toggle {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    z-index: 2;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
}

.checkbox-container input {
    margin-right: 8px;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-btn:hover {
    transform: translateY(-2px);
}

.login-btn i {
    margin-right: 10px;
}

/* Quick Access */
.quick-access {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    color: white;
    margin-bottom: 20px;
    width: 100%;
    max-width: 450px;
}

.quick-access h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.quick-access-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.quick-access-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.quick-access-item:hover {
    transform: translateY(-3px);
}

.quick-access-item i {
    font-size: 1.5rem;
    margin-bottom: 8px;
    color: #ffd700;
}

.quick-access-item span {
    font-size: 0.9rem;
    font-weight: 500;
}

/* System Info */
.system-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px 25px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    color: white;
    width: 100%;
    max-width: 450px;
    margin-bottom: 20px;
}

.current-time,
.system-status {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.current-time i,
.system-status i {
    margin-right: 8px;
}

.system-status i.online {
    color: #2ecc71;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #667eea;
}

.loading-spinner p {
    font-size: 1.2rem;
}

/* Demo Info */
.demo-info {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.demo-toggle {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: transform 0.3s ease;
}

.demo-toggle:hover {
    transform: translateY(-2px);
}

.demo-toggle i {
    margin-right: 8px;
}

.demo-content {
    position: absolute;
    bottom: 60px;
    right: 0;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 300px;
    border: 1px solid #e1e8ed;
}

.demo-content h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.1rem;
}

.demo-section {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e1e8ed;
}

.demo-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.demo-section h5 {
    color: #667eea;
    margin-bottom: 8px;
    font-size: 1rem;
}

.demo-section p {
    margin: 5px 0;
    font-size: 0.9rem;
    color: #666;
}

/* Dashboard Body */
.dashboard-body {
    background: #f8f9fa;
    overflow-x: hidden;
}

/* Sidebar */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 260px;
    height: 100vh;
    background: #2c3e50;
    color: white;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.employee-sidebar {
    background: #27ae60;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
}

.sidebar-header .logo {
    justify-content: flex-start;
    margin-bottom: 0;
}

.sidebar-header .logo i {
    font-size: 1.8rem;
    margin-right: 12px;
    color: #3498db;
}

.sidebar-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item:hover,
.nav-item.active {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: #3498db;
}

.employee-sidebar .nav-item:hover,
.employee-sidebar .nav-item.active {
    border-left-color: #2ecc71;
}

.nav-item i {
    width: 20px;
    margin-right: 15px;
    font-size: 1.1rem;
}

.sidebar-footer {
    position: absolute;
    bottom: 20px;
    width: 100%;
    padding: 0 20px;
}

.logout-btn {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.logout-btn i {
    margin-right: 10px;
}

/* Main Content */
.main-content {
    margin-left: 260px;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

/* Dashboard Header */
.dashboard-header {
    background: white;
    padding: 0 30px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    margin-right: 20px;
    cursor: pointer;
    color: #666;
    display: none;
}

.dashboard-header h1 {
    font-size: 1.6rem;
    color: #2c3e50;
    font-weight: 600;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 15px;
    color: #666;
}

.search-box input {
    padding: 10px 15px 10px 40px;
    border: 1px solid #ddd;
    border-radius: 25px;
    width: 300px;
    font-size: 0.9rem;
}

.current-time {
    display: flex;
    align-items: center;
    color: #666;
    font-weight: 500;
}

.current-time i {
    margin-right: 8px;
    color: #3498db;
}

.notifications {
    position: relative;
    cursor: pointer;
    color: #666;
    font-size: 1.2rem;
}

.notification-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-profile {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 25px;
    transition: background 0.3s ease;
}

.user-profile:hover {
    background: #f8f9fa;
}

.user-profile img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    margin-right: 10px;
}

.user-profile span {
    margin-right: 10px;
    font-weight: 500;
}

/* Dashboard Content */
.dashboard-content {
    padding: 30px;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.employees { background: linear-gradient(135deg, #3498db, #2980b9); }
.stat-icon.present { background: linear-gradient(135deg, #2ecc71, #27ae60); }
.stat-icon.absent { background: linear-gradient(135deg, #e74c3c, #c0392b); }
.stat-icon.late { background: linear-gradient(135deg, #f39c12, #e67e22); }

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: #2c3e50;
}

.stat-info p {
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive { color: #27ae60; }
.stat-change.negative { color: #e74c3c; }
.stat-change.neutral { color: #95a5a6; }

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container,
.recent-activity {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.chart-container h3,
.recent-activity h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
}

.chart-container canvas {
    max-height: 300px;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 15px 0;
    border-bottom: 1px solid #ecf0f1;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ecf0f1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #666;
}

.activity-info p {
    margin-bottom: 5px;
    color: #2c3e50;
    font-weight: 500;
}

.activity-info span {
    font-size: 0.8rem;
    color: #95a5a6;
}

/* Tables */
.attendance-table {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-header h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th,
table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

table td {
    font-size: 0.9rem;
}

.status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status.present,
.status.completed {
    background: #d4edda;
    color: #155724;
}

.status.absent {
    background: #f8d7da;
    color: #721c24;
}

.status.late {
    background: #fff3cd;
    color: #856404;
}

.status.current {
    background: #d1ecf1;
    color: #0c5460;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn i {
    margin-right: 8px;
}

.btn-small {
    padding: 5px 10px;
    margin: 0 2px;
    border: none;
    border-radius: 4px;
    background: #3498db;
    color: white;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background 0.3s ease;
}

.btn-small:hover {
    background: #2980b9;
}

.btn-icon {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    background: #ecf0f1;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin: 0 2px;
}

.btn-icon:hover {
    background: #3498db;
    color: white;
}

/* Employee Dashboard Specific Styles */
.attendance-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.action-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.action-card:hover {
    transform: translateY(-3px);
}

.action-card.check-in .action-icon {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.action-card.check-out .action-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.action-card.break .action-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.action-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    color: white;
    font-size: 1.5rem;
}

.action-info {
    flex: 1;
}

.action-info h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: #2c3e50;
}

.action-info p {
    color: #666;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.action-btn:hover:not(:disabled) {
    transform: translateY(-2px);
}

.action-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

.action-btn i {
    margin-right: 8px;
}

.action-status {
    text-align: center;
    min-width: 80px;
}

.action-status span {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Today's Summary */
.today-summary {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.today-summary h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.summary-item:hover {
    transform: translateY(-3px);
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 1.2rem;
}

.summary-info h4 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.summary-info span {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    background: #d1ecf1;
    color: #0c5460;
}

/* Weekly Overview */
.weekly-overview {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.weekly-overview h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
}

.week-calendar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.day-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.day-card:hover {
    transform: translateY(-3px);
}

.day-card.today {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.day-header {
    margin-bottom: 10px;
}

.day-name {
    display: block;
    font-size: 0.8rem;
    font-weight: 600;
    color: #666;
    margin-bottom: 5px;
}

.day-card.today .day-name {
    color: rgba(255, 255, 255, 0.8);
}

.day-date {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
}

.day-card.today .day-date {
    color: white;
}

.day-status {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.day-status i {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.day-status.present i {
    color: #2ecc71;
}

.day-status.late i {
    color: #f39c12;
}

.day-status.current i {
    color: white;
}

.day-status span {
    font-size: 0.8rem;
    font-weight: 500;
    color: #666;
}

.day-card.today .day-status span {
    color: rgba(255, 255, 255, 0.9);
}

/* Employee Management */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.section-header h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 600;
}

.employees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.employee-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.employee-card:hover {
    transform: translateY(-3px);
}

.employee-avatar {
    margin-right: 15px;
}

.employee-avatar img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.employee-info {
    flex: 1;
}

.employee-info h4 {
    font-size: 1.1rem;
    margin-bottom: 5px;
    color: #2c3e50;
    font-weight: 600;
}

.employee-info p {
    color: #666;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.employee-id {
    font-size: 0.8rem;
    color: #95a5a6;
    font-weight: 500;
}

.employee-status {
    margin: 0 15px;
}

.employee-actions {
    display: flex;
    gap: 5px;
}

/* Attendance Stats */
.attendance-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-item h4 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 10px;
    font-weight: 500;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
}

.stat-value.present {
    color: #2ecc71;
}

.stat-value.absent {
    color: #e74c3c;
}

.stat-value.late {
    color: #f39c12;
}

.stat-value.rate {
    color: #3498db;
}

/* Profile Styles */
.profile-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.profile-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 40px;
    display: flex;
    align-items: center;
}

.profile-avatar {
    position: relative;
    margin-right: 30px;
}

.profile-avatar img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    object-fit: cover;
}

.avatar-edit {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: white;
    border: none;
    color: #3498db;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.profile-info h2 {
    font-size: 2rem;
    margin-bottom: 5px;
    font-weight: 600;
}

.profile-info p {
    font-size: 1.1rem;
    margin-bottom: 10px;
    opacity: 0.9;
}

.profile-details {
    padding: 40px;
}

.detail-section {
    margin-bottom: 30px;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.detail-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-item label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.detail-item span {
    font-size: 1rem;
    color: #2c3e50;
    font-weight: 600;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #ecf0f1;
}

.modal-header h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: #f8f9fa;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 25px;
    border-top: 1px solid #ecf0f1;
}

.modal-form {
    padding: 25px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 30px;
}

.confirmation-info {
    text-align: center;
    padding: 20px;
}

.time-display {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: #3498db;
    margin-bottom: 20px;
}

.time-display i {
    margin-right: 10px;
}

.date-filter {
    display: flex;
    align-items: center;
    gap: 15px;
}

.date-filter input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .search-box input {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar-toggle {
        display: block !important;
    }

    .dashboard-header {
        padding: 0 15px;
    }

    .dashboard-content {
        padding: 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .attendance-actions {
        grid-template-columns: 1fr;
    }

    .summary-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .week-calendar {
        grid-template-columns: repeat(3, 1fr);
    }

    .employees-grid {
        grid-template-columns: 1fr;
    }

    .employee-card {
        flex-direction: column;
        text-align: center;
    }

    .employee-avatar {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .employee-actions {
        margin-top: 15px;
        justify-content: center;
    }

    .profile-header {
        flex-direction: column;
        text-align: center;
    }

    .profile-avatar {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .search-box {
        display: none;
    }

    .header-right {
        gap: 10px;
    }

    .user-profile span {
        display: none;
    }

    .quick-access-grid {
        grid-template-columns: 1fr;
    }

    .login-form-container {
        padding: 30px 20px;
    }

    .demo-content {
        width: 250px;
        right: -50px;
    }
}

@media (max-width: 480px) {
    .login-type-selector {
        flex-direction: column;
        gap: 10px;
    }

    .login-type-btn {
        justify-content: center;
    }

    .action-card {
        flex-direction: column;
        text-align: center;
    }

    .action-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .action-status {
        margin-top: 15px;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .week-calendar {
        grid-template-columns: repeat(2, 1fr);
    }

    .attendance-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .table-container {
        font-size: 0.8rem;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .profile-details {
        padding: 20px;
    }
}

/* Animation Classes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}

.p-20 {
    padding: 20px;
}

.border-radius-10 {
    border-radius: 10px;
}

.box-shadow {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}
