<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechCorp Digital Attendance System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo i {
            font-size: 2rem;
            color: #667eea;
        }

        .logo h1 {
            color: #333;
            font-size: 1.8rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .datetime {
            text-align: right;
        }

        .current-time {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .current-date {
            color: #666;
            font-size: 0.9rem;
        }

        /* Navigation */
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-tab {
            flex: 1;
            min-width: 150px;
            padding: 12px 20px;
            background: transparent;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #666;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(102, 126, 234, 0.1);
        }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Check-in/Check-out Section */
        .attendance-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .attendance-status {
            margin-bottom: 2rem;
        }

        .status-indicator {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
        }

        .status-in {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }

        .status-out {
            background: linear-gradient(135deg, #f44336, #da190b);
        }

        .status-pending {
            background: linear-gradient(135deg, #ff9800, #f57c00);
        }

        .attendance-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .btn-checkin {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-checkout {
            background: linear-gradient(135deg, #f44336, #da190b);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Dashboard Stats */
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #667eea;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        /* Charts */
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            text-align: center;
            color: #333;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        /* Attendance Table */
        .table-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .attendance-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .attendance-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            border-radius: 8px 8px 0 0;
        }

        .attendance-table td {
            padding: 12px;
            border-bottom: 1px solid #eee;
        }

        .attendance-table tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-present {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-absent {
            background: #ffebee;
            color: #c62828;
        }

        .status-late {
            background: #fff3e0;
            color: #ef6c00;
        }

        /* Employee Management */
        .employee-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .employee-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .employee-card:hover {
            transform: translateY(-5px);
        }

        .employee-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 1rem;
        }

        .employee-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .employee-info h3 {
            color: #333;
            margin-bottom: 5px;
        }

        .employee-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .employee-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 1rem;
        }

        .employee-stat {
            text-align: center;
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }

        .employee-stat-number {
            font-weight: bold;
            color: #667eea;
        }

        .employee-stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                min-width: auto;
            }

            .attendance-buttons {
                flex-direction: column;
                align-items: center;
            }

            .dashboard-stats {
                grid-template-columns: 1fr;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }

            .employee-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-card, .chart-container, .employee-card {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Loading Spinner */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-building"></i>
                    <h1>TechCorp Attendance System</h1>
                </div>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">JD</div>
                    <div>
                        <div style="font-weight: 600;">John Doe</div>
                        <div style="color: #666; font-size: 0.9rem;">Software Developer</div>
                    </div>
                </div>
                <div class="datetime">
                    <div class="current-time" id="currentTime">--:--:--</div>
                    <div class="current-date" id="currentDate">Loading...</div>
                </div>
            </div>
        </header>

        <!-- Navigation Tabs -->
        <nav class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('attendance')">
                <i class="fas fa-clock"></i> Attendance
            </button>
            <button class="nav-tab" onclick="showTab('dashboard')">
                <i class="fas fa-chart-dashboard"></i> Dashboard
            </button>
            <button class="nav-tab" onclick="showTab('reports')">
                <i class="fas fa-chart-bar"></i> Reports
            </button>
            <button class="nav-tab" onclick="showTab('employees')">
                <i class="fas fa-users"></i> Employees
            </button>
        </nav>

        <!-- Attendance Tab -->
        <div id="attendance" class="tab-content active">
            <div class="attendance-card">
                <div class="attendance-status">
                    <div class="status-indicator status-pending" id="statusIndicator">
                        <i class="fas fa-clock" id="statusIcon"></i>
                    </div>
                    <h2 id="statusText">Ready to Check In</h2>
                    <p id="statusTime">Click the button below to mark your attendance</p>
                </div>
                <div class="attendance-buttons">
                    <button class="btn btn-checkin" id="checkinBtn" onclick="checkIn()">
                        <i class="fas fa-sign-in-alt"></i> Check In
                    </button>
                    <button class="btn btn-checkout" id="checkoutBtn" onclick="checkOut()" disabled>
                        <i class="fas fa-sign-out-alt"></i> Check Out
                    </button>
                </div>
            </div>

            <!-- Today's Summary -->
            <div class="table-container">
                <h3>Today's Attendance Summary</h3>
                <table class="attendance-table">
                    <thead>
                        <tr>
                            <th>Employee</th>
                            <th>Check In</th>
                            <th>Check Out</th>
                            <th>Hours Worked</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="todayAttendance">
                        <!-- Dynamic content will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>

    <script>
        // Global variables
        let currentUser = {
            id: 'emp001',
            name: 'John Doe',
            role: 'Software Developer',
            isCheckedIn: false,
            checkInTime: null,
            checkOutTime: null
        };

        let attendanceData = [];
        let employeeData = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            updateDateTime();
            setInterval(updateDateTime, 1000);
            loadSampleData();
            updateTodayAttendance();
        });

        function initializeApp() {
            // Load user data from localStorage if available
            const savedUser = localStorage.getItem('currentUser');
            if (savedUser) {
                currentUser = JSON.parse(savedUser);
                updateAttendanceStatus();
            }

            // Initialize user avatar
            const initials = currentUser.name.split(' ').map(n => n[0]).join('');
            document.getElementById('userAvatar').textContent = initials;
        }

        function updateDateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', { 
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            document.getElementById('currentTime').textContent = timeString;
            document.getElementById('currentDate').textContent = dateString;
        }

        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // Remove active class from all nav tabs
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');

            // Load specific tab data
            switch(tabName) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'reports':
                    loadReports();
                    break;
                case 'employees':
                    loadEmployees();
                    break;
            }
        }

        function checkIn() {
            const now = new Date();
            currentUser.isCheckedIn = true;
            currentUser.checkInTime = now.toISOString();
            currentUser.checkOutTime = null;

            // Update UI
            updateAttendanceStatus();
            
            // Save to localStorage
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            // Add to attendance data
            addAttendanceRecord('checkin', now);

            // Show success message
            showNotification('Checked in successfully!', 'success');
        }

        function checkOut() {
            const now = new Date();
            currentUser.isCheckedIn = false;
            currentUser.checkOutTime = now.toISOString();

            // Update UI
            updateAttendanceStatus();
            
            // Save to localStorage
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            // Add to attendance data
            addAttendanceRecord('checkout', now);

            // Calculate hours worked
            const checkInTime = new Date(currentUser.checkInTime);
            const hoursWorked = ((now - checkInTime) / (1000 * 60 * 60)).toFixed(2);

            // Show success message
            showNotification(`Checked out successfully! Hours worked: ${hoursWorked}`, 'success');
        }

        function updateAttendanceStatus() {
            const statusIndicator = document.getElementById('statusIndicator');
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            const statusTime = document.getElementById('statusTime');
            const checkinBtn = document.getElementById('checkinBtn');
            const checkoutBtn = document.getElementById('checkoutBtn');

            if (currentUser.isCheckedIn) {
                statusIndicator.className = 'status-indicator status-in';
                statusIcon.className = 'fas fa-check';
                statusText.textContent = 'Checked In';
                statusTime.textContent = `Since ${new Date(currentUser.checkInTime).toLocaleTimeString()}`;
                checkinBtn.disabled = true;
                checkoutBtn.disabled = false;
            } else {
                statusIndicator.className = 'status-indicator status-pending';
                statusIcon.className = 'fas fa-clock';
                statusText.textContent = 'Ready to Check In';
                statusTime.textContent = 'Click the button below to mark your attendance';
                checkinBtn.disabled = false;
                checkoutBtn.disabled = true;
            }
        }

        function addAttendanceRecord(type, timestamp) {
            const record = {
                employeeId: currentUser.id,
                employeeName: currentUser.name,
                type: type,
                timestamp: timestamp,
                date: timestamp.toDateString()
            };

            attendanceData.push(record);
            updateTodayAttendance();
        }

        function loadSampleData() {
            // Sample employee data
            employeeData = [
                { id: 'emp001', name: 'John Doe', role: 'Software Developer', department: 'Engineering' },
                { id: 'emp002', name: 'Jane Smith', role: 'UI/UX Designer', department: 'Design' },
                { id: 'emp003', name: 'Mike Johnson', role: 'Project Manager', department: 'Management' },
                { id: 'emp004', name: 'Sarah Wilson', role: 'QA Engineer', department: 'Quality Assurance' },
                { id: 'emp005', name: 'David Brown', role: 'DevOps Engineer', department: 'Operations' },
                { id: 'emp006', name: 'Lisa Davis', role: 'Business Analyst', department: 'Business' }
            ];

            // Sample attendance data for today
            const today = new Date();
            const sampleAttendance = [
                { employeeName: 'Jane Smith', checkIn: '09:00', checkOut: '18:00', hours: '9.0', status: 'present' },
                { employeeName: 'Mike Johnson', checkIn: '09:15', checkOut: '17:45', hours: '8.5', status: 'present' },
                { employeeName: 'Sarah Wilson', checkIn: '09:30', checkOut: '--', hours: '--', status: 'late' },
                { employeeName: 'David Brown', checkIn: '--', checkOut: '--', hours: '--', status: 'absent' },
                { employeeName: 'Lisa Davis', checkIn: '08:45', checkOut: '17:30', hours: '8.75', status: 'present' }
            ];

            // Update today's attendance table
            updateTodayAttendanceTable(sampleAttendance);
        }

        function updateTodayAttendance() {
            // This function would typically fetch real attendance data
            // For demo purposes, we'll use the sample data
        }

        function updateTodayAttendanceTable(data) {
            const tbody = document.getElementById('todayAttendance');
            tbody.innerHTML = '';

            data.forEach(record => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${record.employeeName}</td>
                    <td>${record.checkIn}</td>
                    <td>${record.checkOut}</td>
                    <td>${record.hours}</td>
                    <td><span class="status-badge status-${record.status}">${record.status.charAt(0).toUpperCase() + record.status.slice(1)}</span></td>
                `;
                tbody.appendChild(row);
            });
        }

        function showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : '#f44336'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                z-index: 1000;
                animation: slideInRight 0.3s ease-out;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Remove notification after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Add CSS for notification animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
