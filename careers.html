<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Careers - TechNova Solutions | Join Our Team</title>
    <link rel="stylesheet" href="software-styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-code"></i>
                <span>TechNova Solutions</span>
            </div>
            <ul class="nav-menu">
                <li><a href="software-company.html">Home</a></li>
                <li><a href="software-company.html#about">About</a></li>
                <li><a href="software-company.html#services">Services</a></li>
                <li><a href="software-company.html#portfolio">Portfolio</a></li>
                <li><a href="software-company.html#team">Team</a></li>
                <li><a href="software-company.html#contact">Contact</a></li>
                <li><a href="careers.html" class="careers-btn active">Careers</a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Careers Hero Section -->
    <section class="careers-hero">
        <div class="container">
            <div class="careers-hero-content">
                <h1>Join Our <span class="highlight">Innovative Team</span></h1>
                <p>Be part of a dynamic team that's shaping the future of technology. We're looking for passionate individuals who want to make a difference.</p>
                <div class="hero-stats">
                    <div class="stat">
                        <h3>50+</h3>
                        <p>Team Members</p>
                    </div>
                    <div class="stat">
                        <h3>15+</h3>
                        <p>Open Positions</p>
                    </div>
                    <div class="stat">
                        <h3>4.8/5</h3>
                        <p>Employee Rating</p>
                    </div>
                    <div class="stat">
                        <h3>100%</h3>
                        <p>Remote Friendly</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Work With Us Section -->
    <section class="why-work-with-us">
        <div class="container">
            <h2>Why Work With Us?</h2>
            <div class="benefits-grid">
                <div class="benefit-card">
                    <i class="fas fa-laptop-code"></i>
                    <h3>Cutting-Edge Technology</h3>
                    <p>Work with the latest technologies and frameworks in a modern development environment.</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-users"></i>
                    <h3>Collaborative Culture</h3>
                    <p>Join a supportive team that values collaboration, innovation, and continuous learning.</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-chart-line"></i>
                    <h3>Career Growth</h3>
                    <p>Advance your career with mentorship programs, training opportunities, and clear progression paths.</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-balance-scale"></i>
                    <h3>Work-Life Balance</h3>
                    <p>Flexible working hours, remote work options, and generous vacation policies.</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-heart"></i>
                    <h3>Health & Wellness</h3>
                    <p>Comprehensive health insurance, wellness programs, and mental health support.</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-graduation-cap"></i>
                    <h3>Learning & Development</h3>
                    <p>Conference attendance, online courses, and professional development budget.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Job Listings Section -->
    <section class="job-listings">
        <div class="container">
            <div class="jobs-header">
                <h2>Open Positions</h2>
                <div class="job-filters">
                    <select id="departmentFilter">
                        <option value="all">All Departments</option>
                        <option value="engineering">Engineering</option>
                        <option value="design">Design</option>
                        <option value="product">Product</option>
                        <option value="marketing">Marketing</option>
                        <option value="sales">Sales</option>
                        <option value="hr">Human Resources</option>
                    </select>
                    <select id="locationFilter">
                        <option value="all">All Locations</option>
                        <option value="san-francisco">San Francisco, CA</option>
                        <option value="remote">Remote</option>
                        <option value="hybrid">Hybrid</option>
                    </select>
                    <select id="typeFilter">
                        <option value="all">All Types</option>
                        <option value="full-time">Full-time</option>
                        <option value="part-time">Part-time</option>
                        <option value="contract">Contract</option>
                        <option value="internship">Internship</option>
                    </select>
                </div>
            </div>
            
            <div class="jobs-grid" id="jobsGrid">
                <!-- Senior Full Stack Developer -->
                <div class="job-card" data-department="engineering" data-location="san-francisco" data-type="full-time">
                    <div class="job-header">
                        <h3>Senior Full Stack Developer</h3>
                        <span class="job-type">Full-time</span>
                    </div>
                    <div class="job-details">
                        <p><i class="fas fa-map-marker-alt"></i> San Francisco, CA</p>
                        <p><i class="fas fa-building"></i> Engineering</p>
                        <p><i class="fas fa-dollar-sign"></i> $120,000 - $160,000</p>
                    </div>
                    <p class="job-description">We're looking for an experienced full stack developer to join our core engineering team. You'll work on cutting-edge web applications using React, Node.js, and cloud technologies.</p>
                    <div class="job-skills">
                        <span class="skill-tag">React</span>
                        <span class="skill-tag">Node.js</span>
                        <span class="skill-tag">TypeScript</span>
                        <span class="skill-tag">AWS</span>
                    </div>
                    <button class="apply-btn" onclick="openJobModal('senior-fullstack')">Apply Now</button>
                </div>

                <!-- UX/UI Designer -->
                <div class="job-card" data-department="design" data-location="remote" data-type="full-time">
                    <div class="job-header">
                        <h3>UX/UI Designer</h3>
                        <span class="job-type">Full-time</span>
                    </div>
                    <div class="job-details">
                        <p><i class="fas fa-map-marker-alt"></i> Remote</p>
                        <p><i class="fas fa-building"></i> Design</p>
                        <p><i class="fas fa-dollar-sign"></i> $90,000 - $120,000</p>
                    </div>
                    <p class="job-description">Join our design team to create intuitive and beautiful user experiences. You'll work closely with product managers and developers to bring ideas to life.</p>
                    <div class="job-skills">
                        <span class="skill-tag">Figma</span>
                        <span class="skill-tag">Adobe Creative Suite</span>
                        <span class="skill-tag">Prototyping</span>
                        <span class="skill-tag">User Research</span>
                    </div>
                    <button class="apply-btn" onclick="openJobModal('ux-ui-designer')">Apply Now</button>
                </div>

                <!-- DevOps Engineer -->
                <div class="job-card" data-department="engineering" data-location="hybrid" data-type="full-time">
                    <div class="job-header">
                        <h3>DevOps Engineer</h3>
                        <span class="job-type">Full-time</span>
                    </div>
                    <div class="job-details">
                        <p><i class="fas fa-map-marker-alt"></i> Hybrid</p>
                        <p><i class="fas fa-building"></i> Engineering</p>
                        <p><i class="fas fa-dollar-sign"></i> $110,000 - $140,000</p>
                    </div>
                    <p class="job-description">Help us scale our infrastructure and improve our deployment processes. You'll work with containerization, CI/CD pipelines, and cloud platforms.</p>
                    <div class="job-skills">
                        <span class="skill-tag">Docker</span>
                        <span class="skill-tag">Kubernetes</span>
                        <span class="skill-tag">AWS</span>
                        <span class="skill-tag">Jenkins</span>
                    </div>
                    <button class="apply-btn" onclick="openJobModal('devops-engineer')">Apply Now</button>
                </div>

                <!-- Product Manager -->
                <div class="job-card" data-department="product" data-location="san-francisco" data-type="full-time">
                    <div class="job-header">
                        <h3>Product Manager</h3>
                        <span class="job-type">Full-time</span>
                    </div>
                    <div class="job-details">
                        <p><i class="fas fa-map-marker-alt"></i> San Francisco, CA</p>
                        <p><i class="fas fa-building"></i> Product</p>
                        <p><i class="fas fa-dollar-sign"></i> $130,000 - $170,000</p>
                    </div>
                    <p class="job-description">Lead product strategy and execution for our flagship products. You'll work cross-functionally to define roadmaps and drive product success.</p>
                    <div class="job-skills">
                        <span class="skill-tag">Product Strategy</span>
                        <span class="skill-tag">Agile</span>
                        <span class="skill-tag">Analytics</span>
                        <span class="skill-tag">User Research</span>
                    </div>
                    <button class="apply-btn" onclick="openJobModal('product-manager')">Apply Now</button>
                </div>

                <!-- Frontend Developer -->
                <div class="job-card" data-department="engineering" data-location="remote" data-type="full-time">
                    <div class="job-header">
                        <h3>Frontend Developer</h3>
                        <span class="job-type">Full-time</span>
                    </div>
                    <div class="job-details">
                        <p><i class="fas fa-map-marker-alt"></i> Remote</p>
                        <p><i class="fas fa-building"></i> Engineering</p>
                        <p><i class="fas fa-dollar-sign"></i> $95,000 - $125,000</p>
                    </div>
                    <p class="job-description">Build responsive and interactive user interfaces using modern frontend technologies. You'll collaborate with designers and backend developers.</p>
                    <div class="job-skills">
                        <span class="skill-tag">React</span>
                        <span class="skill-tag">Vue.js</span>
                        <span class="skill-tag">CSS3</span>
                        <span class="skill-tag">JavaScript</span>
                    </div>
                    <button class="apply-btn" onclick="openJobModal('frontend-developer')">Apply Now</button>
                </div>

                <!-- Data Scientist -->
                <div class="job-card" data-department="engineering" data-location="san-francisco" data-type="full-time">
                    <div class="job-header">
                        <h3>Data Scientist</h3>
                        <span class="job-type">Full-time</span>
                    </div>
                    <div class="job-details">
                        <p><i class="fas fa-map-marker-alt"></i> San Francisco, CA</p>
                        <p><i class="fas fa-building"></i> Engineering</p>
                        <p><i class="fas fa-dollar-sign"></i> $125,000 - $155,000</p>
                    </div>
                    <p class="job-description">Apply machine learning and statistical analysis to solve complex business problems. You'll work with large datasets and build predictive models.</p>
                    <div class="job-skills">
                        <span class="skill-tag">Python</span>
                        <span class="skill-tag">Machine Learning</span>
                        <span class="skill-tag">SQL</span>
                        <span class="skill-tag">TensorFlow</span>
                    </div>
                    <button class="apply-btn" onclick="openJobModal('data-scientist')">Apply Now</button>
                </div>

                <!-- Marketing Manager -->
                <div class="job-card" data-department="marketing" data-location="hybrid" data-type="full-time">
                    <div class="job-header">
                        <h3>Marketing Manager</h3>
                        <span class="job-type">Full-time</span>
                    </div>
                    <div class="job-details">
                        <p><i class="fas fa-map-marker-alt"></i> Hybrid</p>
                        <p><i class="fas fa-building"></i> Marketing</p>
                        <p><i class="fas fa-dollar-sign"></i> $85,000 - $110,000</p>
                    </div>
                    <p class="job-description">Drive marketing strategy and campaigns to grow our brand and customer base. You'll manage digital marketing channels and analyze performance metrics.</p>
                    <div class="job-skills">
                        <span class="skill-tag">Digital Marketing</span>
                        <span class="skill-tag">SEO/SEM</span>
                        <span class="skill-tag">Analytics</span>
                        <span class="skill-tag">Content Strategy</span>
                    </div>
                    <button class="apply-btn" onclick="openJobModal('marketing-manager')">Apply Now</button>
                </div>

                <!-- Software Engineering Intern -->
                <div class="job-card" data-department="engineering" data-location="san-francisco" data-type="internship">
                    <div class="job-header">
                        <h3>Software Engineering Intern</h3>
                        <span class="job-type">Internship</span>
                    </div>
                    <div class="job-details">
                        <p><i class="fas fa-map-marker-alt"></i> San Francisco, CA</p>
                        <p><i class="fas fa-building"></i> Engineering</p>
                        <p><i class="fas fa-dollar-sign"></i> $25 - $35/hour</p>
                    </div>
                    <p class="job-description">Join our summer internship program and gain hands-on experience in software development. You'll work on real projects with mentorship from senior engineers.</p>
                    <div class="job-skills">
                        <span class="skill-tag">Programming</span>
                        <span class="skill-tag">Computer Science</span>
                        <span class="skill-tag">Problem Solving</span>
                        <span class="skill-tag">Teamwork</span>
                    </div>
                    <button class="apply-btn" onclick="openJobModal('software-intern')">Apply Now</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Application Process Section -->
    <section class="application-process">
        <div class="container">
            <h2>Application Process</h2>
            <div class="process-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h3>Apply Online</h3>
                    <p>Submit your application through our online portal with your resume and cover letter.</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h3>Initial Screening</h3>
                    <p>Our HR team will review your application and conduct an initial phone screening.</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h3>Technical Interview</h3>
                    <p>Participate in technical interviews with our engineering team and hiring managers.</p>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <h3>Final Interview</h3>
                    <p>Meet with senior leadership and potential team members for cultural fit assessment.</p>
                </div>
                <div class="step">
                    <div class="step-number">5</div>
                    <h3>Offer & Onboarding</h3>
                    <p>Receive your offer and join our comprehensive onboarding program.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Employee Testimonials -->
    <section class="testimonials">
        <div class="container">
            <h2>What Our Team Says</h2>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"TechNova has provided me with incredible opportunities to grow both technically and professionally. The collaborative environment and cutting-edge projects make every day exciting."</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face" alt="Alex Chen">
                        <div>
                            <h4>Alex Chen</h4>
                            <p>Senior Software Engineer</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"The work-life balance here is amazing. I can work remotely when needed, and the company truly cares about employee wellbeing. It's the best place I've ever worked."</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face" alt="Maria Rodriguez">
                        <div>
                            <h4>Maria Rodriguez</h4>
                            <p>UX Designer</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"The learning opportunities are endless. From conference attendance to internal tech talks, TechNova invests in our professional development like no other company."</p>
                    </div>
                    <div class="testimonial-author">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="David Kim">
                        <div>
                            <h4>David Kim</h4>
                            <p>DevOps Engineer</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Job Application Modal -->
    <div class="modal hidden" id="jobModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalJobTitle">Apply for Position</h3>
                <button class="modal-close" onclick="closeJobModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="jobApplicationForm" class="application-form">
                    <div class="form-section">
                        <h4>Personal Information</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName">First Name *</label>
                                <input type="text" id="firstName" name="firstName" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName">Last Name *</label>
                                <input type="text" id="lastName" name="lastName" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="phone">Phone Number *</label>
                                <input type="tel" id="phone" name="phone" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="location">Current Location *</label>
                            <input type="text" id="location" name="location" placeholder="City, State/Country" required>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4>Professional Information</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="experience">Years of Experience *</label>
                                <select id="experience" name="experience" required>
                                    <option value="">Select Experience</option>
                                    <option value="0-1">0-1 years</option>
                                    <option value="2-3">2-3 years</option>
                                    <option value="4-6">4-6 years</option>
                                    <option value="7-10">7-10 years</option>
                                    <option value="10+">10+ years</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="currentRole">Current Role</label>
                                <input type="text" id="currentRole" name="currentRole" placeholder="e.g., Software Engineer">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="currentCompany">Current Company</label>
                            <input type="text" id="currentCompany" name="currentCompany">
                        </div>
                        <div class="form-group">
                            <label for="expectedSalary">Expected Salary Range</label>
                            <select id="expectedSalary" name="expectedSalary">
                                <option value="">Select Range</option>
                                <option value="60k-80k">$60,000 - $80,000</option>
                                <option value="80k-100k">$80,000 - $100,000</option>
                                <option value="100k-120k">$100,000 - $120,000</option>
                                <option value="120k-150k">$120,000 - $150,000</option>
                                <option value="150k+">$150,000+</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4>Application Materials</h4>
                        <div class="form-group">
                            <label for="resume">Resume/CV *</label>
                            <div class="file-upload">
                                <input type="file" id="resume" name="resume" accept=".pdf,.doc,.docx" required>
                                <label for="resume" class="file-upload-label">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>Choose file or drag and drop</span>
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="portfolio">Portfolio/Website</label>
                            <input type="url" id="portfolio" name="portfolio" placeholder="https://yourportfolio.com">
                        </div>
                        <div class="form-group">
                            <label for="linkedin">LinkedIn Profile</label>
                            <input type="url" id="linkedin" name="linkedin" placeholder="https://linkedin.com/in/yourprofile">
                        </div>
                        <div class="form-group">
                            <label for="github">GitHub Profile</label>
                            <input type="url" id="github" name="github" placeholder="https://github.com/yourusername">
                        </div>
                    </div>

                    <div class="form-section">
                        <h4>Additional Information</h4>
                        <div class="form-group">
                            <label for="coverLetter">Cover Letter</label>
                            <textarea id="coverLetter" name="coverLetter" rows="6" placeholder="Tell us why you're interested in this position and what makes you a great fit..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="availability">When can you start?</label>
                            <select id="availability" name="availability">
                                <option value="">Select Availability</option>
                                <option value="immediately">Immediately</option>
                                <option value="2-weeks">2 weeks notice</option>
                                <option value="1-month">1 month</option>
                                <option value="2-months">2 months</option>
                                <option value="negotiable">Negotiable</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="workAuthorization">Work Authorization</label>
                            <select id="workAuthorization" name="workAuthorization" required>
                                <option value="">Select Status</option>
                                <option value="us-citizen">US Citizen</option>
                                <option value="permanent-resident">Permanent Resident</option>
                                <option value="h1b">H1B Visa</option>
                                <option value="opt">OPT/CPT</option>
                                <option value="other">Other</option>
                                <option value="require-sponsorship">Require Sponsorship</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeJobModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Submit Application</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-code"></i>
                        <span>TechNova Solutions</span>
                    </div>
                    <p>Join our team and help us transform ideas into digital reality.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="software-company.html">Home</a></li>
                        <li><a href="software-company.html#about">About</a></li>
                        <li><a href="software-company.html#services">Services</a></li>
                        <li><a href="software-company.html#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>HR Contact</h4>
                    <div class="contact-details">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +****************</p>
                        <p><i class="fas fa-map-marker-alt"></i> 123 Tech Street, San Francisco, CA</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <p>&copy; 2024 TechNova Solutions. All rights reserved.</p>
                    <div class="footer-links">
                        <a href="#">Privacy Policy</a>
                        <a href="#">Equal Opportunity</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="software-script.js"></script></script>
</body>
</html>
