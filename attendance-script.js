// Digital Attendance System JavaScript

// Global variables
let currentUser = null;
let attendanceData = {};
let isOnBreak = false;
let breakStartTime = null;
let totalBreakTime = 0;
let workStartTime = null;

// Sample data
const sampleUsers = {
    employees: {
        'EMP001': {
            id: 'EMP001',
            name: '<PERSON>',
            email: '<EMAIL>',
            department: 'IT',
            position: 'Software Developer',
            password: 'employee123'
        },
        'EMP002': {
            id: 'EMP002',
            name: '<PERSON>',
            email: '<EMAIL>',
            department: 'HR',
            position: 'HR Manager',
            password: 'employee123'
        }
    },
    hr: {
        'hr_admin': {
            username: 'hr_admin',
            name: 'HR Administrator',
            email: '<EMAIL>',
            department: 'hr',
            password: 'hr123'
        }
    }
};

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    
    // Check if we're on a specific page
    const currentPage = window.location.pathname.split('/').pop();
    
    if (currentPage === 'attendance.html' || currentPage === '') {
        initializeLoginPage();
    } else if (currentPage === 'hr-dashboard.html') {
        initializeHRDashboard();
    } else if (currentPage === 'employee-dashboard.html') {
        initializeEmployeeDashboard();
    }
}

function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour12: true,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    const timeElements = document.querySelectorAll('#current-time, #currentTime');
    timeElements.forEach(element => {
        if (element) {
            element.textContent = timeString;
        }
    });
}

// Login Page Functions
function initializeLoginPage() {
    setupLoginTypeSelector();
    setupLoginForms();
    updateCurrentTime();
}

function setupLoginTypeSelector() {
    const loginTypeBtns = document.querySelectorAll('.login-type-btn');
    const loginContainers = document.querySelectorAll('.login-form-container');
    
    loginTypeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const type = this.getAttribute('data-type');
            
            // Update active button
            loginTypeBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Show/hide login forms
            loginContainers.forEach(container => {
                container.classList.add('hidden');
            });
            
            const targetContainer = document.getElementById(`${type}-login`);
            if (targetContainer) {
                targetContainer.classList.remove('hidden');
            }
        });
    });
}

function setupLoginForms() {
    const employeeForm = document.getElementById('employeeLoginForm');
    const hrForm = document.getElementById('hrLoginForm');
    
    if (employeeForm) {
        employeeForm.addEventListener('submit', handleEmployeeLogin);
    }
    
    if (hrForm) {
        hrForm.addEventListener('submit', handleHRLogin);
    }
}

function handleEmployeeLogin(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const empId = formData.get('empId');
    const password = formData.get('password');
    
    showLoadingOverlay();
    
    setTimeout(() => {
        const employee = sampleUsers.employees[empId];
        
        if (employee && employee.password === password) {
            currentUser = { ...employee, type: 'employee' };
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            showNotification('Login successful! Redirecting...', 'success');
            
            setTimeout(() => {
                window.location.href = 'employee-dashboard.html';
            }, 1500);
        } else {
            hideLoadingOverlay();
            showNotification('Invalid Employee ID or password', 'error');
        }
    }, 2000);
}

function handleHRLogin(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const username = formData.get('username');
    const password = formData.get('password');
    const department = formData.get('department');
    
    showLoadingOverlay();
    
    setTimeout(() => {
        const hrUser = sampleUsers.hr[username];
        
        if (hrUser && hrUser.password === password && hrUser.department === department) {
            currentUser = { ...hrUser, type: 'hr' };
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            showNotification('HR Login successful! Redirecting...', 'success');
            
            setTimeout(() => {
                window.location.href = 'hr-dashboard.html';
            }, 1500);
        } else {
            hideLoadingOverlay();
            showNotification('Invalid HR credentials', 'error');
        }
    }, 2000);
}

function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function toggleDemoInfo() {
    const demoContent = document.getElementById('demoContent');
    demoContent.classList.toggle('hidden');
}

// Dashboard Common Functions
function initializeSidebar() {
    const navItems = document.querySelectorAll('.nav-item');
    const contentSections = document.querySelectorAll('.content-section');
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    
    // Navigation
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            
            // Update active nav item
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            
            // Show target section
            contentSections.forEach(section => section.classList.remove('active'));
            const targetSection = document.getElementById(targetId);
            if (targetSection) {
                targetSection.classList.add('active');
            }
        });
    });
    
    // Mobile sidebar toggle
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
    }
}

// HR Dashboard Functions
function initializeHRDashboard() {
    // Check if user is authorized
    const user = JSON.parse(localStorage.getItem('currentUser'));
    if (!user || user.type !== 'hr') {
        window.location.href = 'attendance.html';
        return;
    }
    
    currentUser = user;
    initializeSidebar();
    initializeHRCharts();
    loadHRData();
}

function initializeHRCharts() {
    const ctx = document.getElementById('attendanceChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
            datasets: [{
                label: 'Present',
                data: [142, 138, 145, 140, 142, 85],
                borderColor: '#2ecc71',
                backgroundColor: 'rgba(46, 204, 113, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }, {
                label: 'Absent',
                data: [14, 18, 11, 16, 14, 71],
                borderColor: '#e74c3c',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

function loadHRData() {
    // Simulate loading HR dashboard data
    animateCounters();
    updateAttendanceTable();
}

function animateCounters() {
    const counters = document.querySelectorAll('.stat-info h3');
    
    counters.forEach(counter => {
        const target = parseInt(counter.textContent);
        const increment = target / 50;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            counter.textContent = Math.floor(current);
        }, 30);
    });
}

function updateAttendanceTable() {
    // This would typically fetch real data from a server
    console.log('Attendance table updated');
}

function showAddEmployeeModal() {
    const modal = document.getElementById('addEmployeeModal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
    }
}

// Employee Dashboard Functions
function initializeEmployeeDashboard() {
    // Check if user is authorized
    const user = JSON.parse(localStorage.getItem('currentUser'));
    if (!user || user.type !== 'employee') {
        window.location.href = 'attendance.html';
        return;
    }
    
    currentUser = user;
    initializeSidebar();
    loadEmployeeData();
    updateWorkingHours();
    setInterval(updateWorkingHours, 60000); // Update every minute
}

function loadEmployeeData() {
    // Load today's attendance data
    const today = new Date().toDateString();
    attendanceData = JSON.parse(localStorage.getItem(`attendance_${currentUser.id}_${today}`)) || {};
    
    updateAttendanceUI();
}

function updateAttendanceUI() {
    const checkInBtn = document.getElementById('checkInBtn');
    const checkOutBtn = document.getElementById('checkOutBtn');
    const breakBtn = document.getElementById('breakBtn');
    const checkInTime = document.getElementById('checkInTime');
    const checkOutTime = document.getElementById('checkOutTime');
    const todayStatus = document.getElementById('todayStatus');
    
    if (attendanceData.checkIn) {
        checkInTime.textContent = attendanceData.checkIn;
        checkInBtn.disabled = true;
        checkInBtn.innerHTML = '<i class="fas fa-check"></i> Checked In';
        checkOutBtn.disabled = false;
        todayStatus.textContent = 'Working';
        todayStatus.className = 'status-badge present';
        workStartTime = new Date(attendanceData.checkInTimestamp);
    }
    
    if (attendanceData.checkOut) {
        checkOutTime.textContent = attendanceData.checkOut;
        checkOutBtn.disabled = true;
        checkOutBtn.innerHTML = '<i class="fas fa-check"></i> Checked Out';
        breakBtn.disabled = true;
        todayStatus.textContent = 'Completed';
        todayStatus.className = 'status-badge completed';
    }
    
    if (attendanceData.breaks) {
        totalBreakTime = attendanceData.breaks.reduce((total, breakItem) => {
            return total + (breakItem.duration || 0);
        }, 0);
        updateBreakDisplay();
    }
}

function checkIn() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour12: true,
        hour: '2-digit',
        minute: '2-digit'
    });

    showAttendanceModal('Check In', timeString, 'Are you sure you want to check in now?', function() {
        attendanceData.checkIn = timeString;
        attendanceData.checkInTimestamp = now.toISOString();
        workStartTime = now;

        saveAttendanceData();
        updateAttendanceUI();
        closeModal('attendanceModal');
        showNotification('Successfully checked in!', 'success');
    });
}

function checkOut() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour12: true,
        hour: '2-digit',
        minute: '2-digit'
    });

    showAttendanceModal('Check Out', timeString, 'Are you sure you want to check out now?', function() {
        attendanceData.checkOut = timeString;
        attendanceData.checkOutTimestamp = now.toISOString();

        // End break if currently on break
        if (isOnBreak) {
            endBreak();
        }

        saveAttendanceData();
        updateAttendanceUI();
        closeModal('attendanceModal');
        showNotification('Successfully checked out!', 'success');
    });
}

function toggleBreak() {
    if (isOnBreak) {
        endBreak();
    } else {
        startBreak();
    }
}

function startBreak() {
    const now = new Date();
    breakStartTime = now;
    isOnBreak = true;

    const breakBtn = document.getElementById('breakBtn');
    breakBtn.innerHTML = '<i class="fas fa-play"></i> End Break';

    showNotification('Break started', 'info');
}

function endBreak() {
    if (!breakStartTime) return;

    const now = new Date();
    const breakDuration = Math.floor((now - breakStartTime) / (1000 * 60)); // in minutes

    if (!attendanceData.breaks) {
        attendanceData.breaks = [];
    }

    attendanceData.breaks.push({
        start: breakStartTime.toISOString(),
        end: now.toISOString(),
        duration: breakDuration
    });

    totalBreakTime += breakDuration;
    isOnBreak = false;
    breakStartTime = null;

    const breakBtn = document.getElementById('breakBtn');
    breakBtn.innerHTML = '<i class="fas fa-pause"></i> Start Break';

    updateBreakDisplay();
    saveAttendanceData();
    showNotification(`Break ended. Duration: ${breakDuration} minutes`, 'info');
}

function updateBreakDisplay() {
    const breakTimeElement = document.getElementById('breakTime');
    const totalBreakTimeElement = document.getElementById('totalBreakTime');

    if (isOnBreak && breakStartTime) {
        const currentBreakTime = Math.floor((new Date() - breakStartTime) / (1000 * 60));
        if (breakTimeElement) {
            breakTimeElement.textContent = `${currentBreakTime}m`;
        }
    } else {
        if (breakTimeElement) {
            breakTimeElement.textContent = '0m';
        }
    }

    if (totalBreakTimeElement) {
        const hours = Math.floor(totalBreakTime / 60);
        const minutes = totalBreakTime % 60;
        totalBreakTimeElement.textContent = `${hours}h ${minutes}m`;
    }
}

function updateWorkingHours() {
    if (!workStartTime) return;

    const now = new Date();
    let workingMinutes = Math.floor((now - workStartTime) / (1000 * 60));

    // Subtract break time
    workingMinutes -= totalBreakTime;

    // Subtract current break time if on break
    if (isOnBreak && breakStartTime) {
        const currentBreakTime = Math.floor((now - breakStartTime) / (1000 * 60));
        workingMinutes -= currentBreakTime;
    }

    const hours = Math.floor(workingMinutes / 60);
    const minutes = workingMinutes % 60;

    const workingHoursElement = document.getElementById('workingHours');
    if (workingHoursElement) {
        workingHoursElement.textContent = `${hours}h ${minutes}m`;
    }

    // Update productivity
    const productivityElement = document.getElementById('productivity');
    if (productivityElement) {
        const expectedHours = 8;
        const actualHours = workingMinutes / 60;
        const productivity = Math.min(100, Math.round((actualHours / expectedHours) * 100));
        productivityElement.textContent = `${productivity}%`;
    }

    // Update break display
    updateBreakDisplay();
}

function saveAttendanceData() {
    const today = new Date().toDateString();
    localStorage.setItem(`attendance_${currentUser.id}_${today}`, JSON.stringify(attendanceData));
}

function showAttendanceModal(title, time, message, confirmCallback) {
    const modal = document.getElementById('attendanceModal');
    const modalTitle = document.getElementById('modalTitle');
    const modalTime = document.getElementById('modalTime');
    const modalMessage = document.getElementById('modalMessage');
    const confirmBtn = document.getElementById('confirmBtn');

    if (modal && modalTitle && modalTime && modalMessage && confirmBtn) {
        modalTitle.textContent = title;
        modalTime.textContent = time;
        modalMessage.textContent = message;

        // Remove existing event listeners
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        // Add new event listener
        newConfirmBtn.addEventListener('click', confirmCallback);

        modal.classList.remove('hidden');
    }
}

// Utility Functions
function showLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.remove('hidden');
    }
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.add('hidden');
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;

    // Add styles if not already present
    if (!document.querySelector('.notification-styles')) {
        const style = document.createElement('style');
        style.className = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            }

            .notification.show {
                opacity: 1;
                transform: translateX(0);
            }

            .notification-content {
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                display: flex;
                align-items: center;
                justify-content: space-between;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            }

            .notification-success .notification-content {
                background: #27ae60;
            }

            .notification-error .notification-content {
                background: #e74c3c;
            }

            .notification-info .notification-content {
                background: #3498db;
            }

            .notification-close {
                background: none;
                border: none;
                color: white;
                font-size: 1.2rem;
                cursor: pointer;
                margin-left: 15px;
            }
        `;
        document.head.appendChild(style);
    }

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // Auto hide after 5 seconds
    setTimeout(() => {
        hideNotification(notification);
    }, 5000);

    // Close button functionality
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        hideNotification(notification);
    });
}

function hideNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// Export functions for global access
window.attendanceSystem = {
    togglePassword,
    toggleDemoInfo,
    checkIn,
    checkOut,
    toggleBreak,
    showAddEmployeeModal,
    closeModal,
    showNotification
};
